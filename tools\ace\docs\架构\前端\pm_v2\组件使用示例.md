# Project Manager V2 组件使用示例

## 1. 通用组件使用示例

### 1.1 进度条组件

#### 基础用法
```html
<!-- 基础进度条 -->
<div class="progress-bar">
    <div class="progress-fill success" style="width: 75%;"></div>
</div>

<!-- 带标签的进度条 -->
<div style="margin-bottom: 0.5rem;">
    <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
        <span>已发现原子约束</span>
        <span style="color: #4CAF50; font-weight: bold;">25</span>
    </div>
    <div class="progress-bar">
        <div class="progress-fill success" style="width: 62.5%;"></div>
    </div>
</div>
```

#### 不同颜色主题
```html
<!-- 成功状态 -->
<div class="progress-bar">
    <div class="progress-fill success" style="width: 80%;"></div>
</div>

<!-- 警告状态 -->
<div class="progress-bar">
    <div class="progress-fill warning" style="width: 60%;"></div>
</div>

<!-- 信息状态 -->
<div class="progress-bar">
    <div class="progress-fill info" style="width: 45%;"></div>
</div>

<!-- 危险状态 -->
<div class="progress-bar">
    <div class="progress-fill danger" style="width: 30%;"></div>
</div>

<!-- 主色调 -->
<div class="progress-bar">
    <div class="progress-fill primary" style="width: 90%;"></div>
</div>
```

#### JavaScript动态更新
```javascript
// 更新进度条
function updateProgress(elementId, percentage, type = 'primary') {
    const progressFill = document.querySelector(`#${elementId} .progress-fill`);
    if (progressFill) {
        progressFill.style.width = `${percentage}%`;
        progressFill.className = `progress-fill ${type}`;
    }
}

// 使用示例
updateProgress('overall-progress', 75, 'success');
```

### 1.2 状态指示器组件

#### 基础用法
```html
<!-- 不同状态的指示器 -->
<div class="status-item">
    <span class="status-indicator status-active"></span>
    <span>服务运行中</span>
</div>

<div class="status-item">
    <span class="status-indicator status-thinking"></span>
    <span>AI正在思考</span>
</div>

<div class="status-item">
    <span class="status-indicator status-converging"></span>
    <span>结果收敛中</span>
</div>

<div class="status-item">
    <span class="status-indicator status-pending"></span>
    <span>等待处理</span>
</div>

<div class="status-item">
    <span class="status-indicator status-error"></span>
    <span>处理失败</span>
</div>
```

#### JavaScript状态切换
```javascript
// 更新状态指示器
function updateStatusIndicator(elementId, status) {
    const indicator = document.getElementById(elementId);
    if (indicator) {
        indicator.className = `status-indicator status-${status}`;
    }
}

// 使用示例
updateStatusIndicator('ai-status', 'thinking');
setTimeout(() => {
    updateStatusIndicator('ai-status', 'active');
}, 3000);
```

### 1.3 圆形进度环组件

#### 完整示例
```html
<div class="circular-progress">
    <svg>
        <circle class="progress-circle progress-bg" cx="40" cy="40" r="36"></circle>
        <circle class="progress-circle progress-bar-circle success" cx="40" cy="40" r="36" 
                style="stroke-dashoffset: 79.1;" id="reliability-circle"></circle>
    </svg>
    <div class="progress-text">
        <div class="progress-value" id="reliability-score">65%</div>
        <div class="progress-label">可靠性</div>
    </div>
</div>
```

#### JavaScript动态更新
```javascript
// 更新圆形进度环
function updateCircularProgress(elementId, percentage, type = 'success') {
    const circle = document.getElementById(elementId);
    const valueElement = document.getElementById(elementId.replace('-circle', '-score'));
    
    if (circle && valueElement) {
        // 计算stroke-dashoffset (226是圆周长)
        const offset = 226 - (226 * percentage / 100);
        circle.style.strokeDashoffset = offset;
        circle.className = `progress-circle progress-bar-circle ${type}`;
        valueElement.textContent = `${percentage}%`;
    }
}

// 使用示例
updateCircularProgress('reliability-circle', 87, 'success');
```

### 1.4 风险列表组件

#### 完整示例
```html
<div id="risk-list">
    <div class="risk-item critical">
        <span class="risk-badge critical">CRITICAL</span>
        <span>核心决策逻辑与全局护栏存在原则性冲突</span>
    </div>
    
    <div class="risk-item high">
        <span class="risk-badge high">HIGH</span>
        <span>UserService 未定义明确的响应时间边界条件</span>
    </div>
    
    <div class="risk-item medium">
        <span class="risk-badge medium">MEDIUM</span>
        <span>数据库连接池配置可能导致性能瓶颈</span>
    </div>
    
    <div class="risk-item low">
        <span class="risk-badge low">LOW</span>
        <span>日志级别配置建议优化</span>
    </div>
</div>
```

#### JavaScript动态添加
```javascript
// 添加风险项
function addRiskItem(containerId, level, message) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    const riskItem = document.createElement('div');
    riskItem.className = `risk-item ${level}`;
    riskItem.innerHTML = `
        <span class="risk-badge ${level}">${level.toUpperCase()}</span>
        <span>${message}</span>
    `;
    
    container.appendChild(riskItem);
}

// 使用示例
addRiskItem('risk-list', 'high', '新发现的安全漏洞需要立即修复');
```

## 2. 专用组件使用示例

### 2.1 知识库可视化组件

#### 完整示例
```html
<div class="knowledge-graph" id="knowledge-graph">
    <!-- 约束节点 -->
    <div class="constraint-node global" style="top: 30px; left: 30px;" 
         onclick="showConstraintDetail('GB001')" 
         onmouseenter="showTooltip(this, 'GB001: 全局性能约束')"
         onmouseleave="hideTooltip()">
        GB001
        <div class="node-label">全局</div>
    </div>
    
    <div class="constraint-node local" style="top: 80px; left: 120px;" 
         onclick="showConstraintDetail('LB002')"
         onmouseenter="showTooltip(this, 'LB002: 用户服务约束')"
         onmouseleave="hideTooltip()">
        LB002
        <div class="node-label">本地</div>
    </div>
    
    <!-- 连接线 -->
    <div class="constraint-connection" style="top: 55px; left: 75px; width: 50px; transform: rotate(15deg);"></div>
    
    <!-- 提示框 -->
    <div class="node-tooltip" id="node-tooltip"></div>
</div>
```

#### JavaScript交互逻辑
```javascript
// 动态添加约束节点
function addConstraintNode(graphId, constraint) {
    const graph = document.getElementById(graphId);
    if (!graph) return;
    
    const node = document.createElement('div');
    node.className = `constraint-node ${constraint.type}`;
    node.style.top = `${constraint.y}px`;
    node.style.left = `${constraint.x}px`;
    node.onclick = () => showConstraintDetail(constraint.id);
    node.onmouseenter = () => showTooltip(node, constraint.description);
    node.onmouseleave = () => hideTooltip();
    node.innerHTML = `
        ${constraint.id}
        <div class="node-label">${constraint.label}</div>
    `;
    
    graph.appendChild(node);
}

// 使用示例
addConstraintNode('knowledge-graph', {
    id: 'BC003',
    type: 'boundary',
    x: 200,
    y: 40,
    label: '边界',
    description: 'BC003: 响应时间边界条件'
});
```

### 2.2 决策中心卡片组件

#### 洞察卡片
```html
<div class="decision-card insight">
    <div class="decision-card-title">
        <span>🔍</span>
        <span>关键洞察</span>
        <span style="margin-left: auto; font-size: 0.7rem; background: rgba(76, 175, 80, 0.2); padding: 0.1rem 0.3rem; border-radius: 3px;">实时</span>
    </div>
    <div id="key-insight" style="line-height: 1.4;">
        <div style="margin-bottom: 0.3rem;">✅ 已完成阶段零标准化验证</div>
        <div style="margin-bottom: 0.3rem;">⚠️ 发现 <strong style="color: #F44336;">2个致命级</strong> 架构风险</div>
        <div style="color: #4CAF50;">📊 当前可靠性评分: <strong>65%</strong></div>
    </div>
</div>
```

#### 决策卡片
```html
<div class="decision-card action" id="action-required">
    <div class="decision-card-title">
        <span>⚠️</span>
        <span>需要项目经理决策</span>
        <span style="margin-left: auto; font-size: 0.7rem; background: rgba(255, 152, 0, 0.2); padding: 0.1rem 0.3rem; border-radius: 3px;">紧急</span>
    </div>
    <div id="decision-question" style="margin-bottom: 0.5rem; line-height: 1.4;">
        项目可靠性评分较低(65%)，且发现致命级风险。是否继续执行治理流程？
    </div>
    <div class="decision-options">
        <button class="decision-btn approve" onclick="makeDecision('approve')">
            ✅ 批准并继续
        </button>
        <button class="decision-btn reject" onclick="makeDecision('reject')">
            ⏸️ 暂停检测
        </button>
    </div>
</div>
```

#### JavaScript决策处理
```javascript
// 处理决策
function makeDecision(decision) {
    const actionRequired = document.getElementById('action-required');
    actionRequired.style.display = 'none';
    
    // 添加决策日志
    addLogEntry('decision-log', `用户选择: ${decision === 'approve' ? '批准并继续' : '暂停检测'}`, 'info');
    
    // 触发相应的业务逻辑
    if (decision === 'approve') {
        continueGovernanceProcess();
    } else {
        pauseGovernanceProcess();
    }
}
```

### 2.3 算法思维日志组件

#### 完整示例
```html
<div id="process-log" style="font-family: monospace; font-size: 0.8rem;">
    <div class="log-entry expandable" onclick="showLogDetail(this, 'startup_check_ide')" 
         style="cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;" 
         onmouseover="this.style.backgroundColor='#393B40'" 
         onmouseout="this.style.backgroundColor='transparent'">
        [14:17:30] 启动检查: 正在验证IDE AI连接状态...✅ 连接正常
        <span class="log-arrows">
            <span class="arrow-ai-comm" onclick="event.stopPropagation(); toggleLogDetail(this, 'ai-comm', 'startup_check_ide')"></span>
            <span class="arrow-py-ops" onclick="event.stopPropagation(); toggleLogDetail(this, 'py-ops', 'startup_check_ide')"></span>
        </span>
    </div>
</div>
```

#### JavaScript日志管理
```javascript
// 添加新的日志条目
function addProcessLogEntry(containerId, message, logId) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    const timestamp = formatTimestamp();
    const logEntry = document.createElement('div');
    logEntry.className = 'log-entry expandable';
    logEntry.style.cssText = 'cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;';
    logEntry.onclick = () => showLogDetail(logEntry, logId);
    logEntry.onmouseover = () => logEntry.style.backgroundColor = '#393B40';
    logEntry.onmouseout = () => logEntry.style.backgroundColor = 'transparent';
    
    logEntry.innerHTML = `
        [${timestamp}] ${message}
        <span class="log-arrows">
            <span class="arrow-ai-comm" onclick="event.stopPropagation(); toggleLogDetail(this, 'ai-comm', '${logId}')"></span>
            <span class="arrow-py-ops" onclick="event.stopPropagation(); toggleLogDetail(this, 'py-ops', '${logId}')"></span>
        </span>
    `;
    
    container.appendChild(logEntry);
    container.scrollTop = container.scrollHeight;
}

// 使用示例
addProcessLogEntry('process-log', '开始执行文档分析...', 'document_analysis_start');
```

## 3. 组合使用示例

### 3.1 完整的区域实现
```html
<!-- 区域1-2: 项目进度监控 -->
<div class="grid-area grid-area-1-2 vscode-scrollbar">
    <div class="area-title">项目进度监控 (Progress Monitoring)</div>
    <div class="area-content">
        <!-- 阶段进度 -->
        <div class="stage-progress">
            <div class="stage-item current">
                <span class="status-indicator status-thinking"></span>
                <span>阶段零：标准化与预验证</span>
            </div>
            <!-- 更多阶段... -->
        </div>
        
        <!-- 关键指标 -->
        <div style="margin-top: 1rem;">
            <div style="font-weight: bold; margin-bottom: 0.5rem;">关键指标:</div>
            
            <div style="margin-bottom: 0.5rem;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                    <span>已发现原子约束</span>
                    <span style="color: #4CAF50; font-weight: bold;">25</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill success" style="width: 62.5%;"></div>
                </div>
            </div>
            
            <!-- 更多指标... -->
        </div>
        
        <!-- 整体进度 -->
        <div style="margin-top: 1rem;">
            <div style="margin-bottom: 0.5rem; font-weight: bold;">整体进度:</div>
            <div style="background: #2A2D30; padding: 0.8rem; border-radius: 4px; border: 1px solid #3C3F41;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 0.3rem;">
                    <span>阶段零进行中</span>
                    <span style="color: #0078D4; font-weight: bold;">25%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill primary" style="width: 25%;"></div>
                </div>
            </div>
        </div>
    </div>
</div>
```

### 3.2 响应式适配示例
```css
/* 桌面端优化 */
@media (min-width: 1200px) {
    .constraint-node {
        width: 50px;
        height: 50px;
        font-size: 0.6rem;
    }
    
    .circular-progress {
        width: 80px;
        height: 80px;
    }
}

/* 平板端适配 */
@media (max-width: 1200px) and (min-width: 768px) {
    .constraint-node {
        width: 40px;
        height: 40px;
        font-size: 0.5rem;
    }
    
    .circular-progress {
        width: 60px;
        height: 60px;
    }
    
    .decision-card {
        padding: 0.6rem;
    }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .nine-grid-container {
        grid-template-columns: 1fr;
        grid-template-areas:
            "area1-2"
            "area3"
            "area4"
            "area5"
            "area6"
            "area7"
            "area8"
            "area9";
    }
    
    .constraint-node {
        width: 30px;
        height: 30px;
        font-size: 0.4rem;
    }
}
```

## 4. 最佳实践总结

### 4.1 组件使用原则
1. **优先使用通用组件** - 减少重复代码，保持一致性
2. **合理组合组件** - 通过组合实现复杂功能
3. **保持响应式** - 确保在不同设备上的良好体验
4. **注重性能** - 避免不必要的DOM操作和重绘
5. **提供反馈** - 及时的视觉反馈提升用户体验

### 4.2 开发建议
1. **先设计后编码** - 明确组件结构和交互逻辑
2. **模块化开发** - 将复杂功能拆分为独立模块
3. **测试驱动** - 为关键组件编写测试用例
4. **文档同步** - 及时更新组件使用文档
5. **代码复用** - 提取可复用的逻辑和样式
