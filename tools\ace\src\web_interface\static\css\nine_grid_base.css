/* 九宫格基础样式 - 提取自nine_grid.html */

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 撑满全屏九宫格布局 - 三等分布局 */
.nine-grid-container {
    width: 100vw;
    height: 100vh;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    grid-gap: 2px;
    overflow: hidden;
    background-color: #1E1F22;
    grid-template-areas:
        "area1-2 area5 area3"
        "area4   area5 area6"
        "area7   area8 area9";
}

/* 九宫格区域基础样式 */
.grid-area {
    background-color: #2A2D30;
    border: 1px solid #3C3F41;
    padding: 0.1rem 1rem 1rem 1rem;
    overflow-y: auto;
    color: #BBBBBB;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 特定区域的grid-area定义 */
.grid-area-1-2 { grid-area: area1-2; }
.grid-area-3 { grid-area: area3; }
.grid-area-4 { grid-area: area4; }
.grid-area-5 { grid-area: area5; }
.grid-area-6 { grid-area: area6; }
.grid-area-7 { grid-area: area7; }
.grid-area-8 { grid-area: area8; }
.grid-area-9 { grid-area: area9; }

/* 区域标题样式 */
.area-title {
    font-size: calc(0.5rem + 0.15vw);
    font-weight: bold;
    color: #0078D4;
    margin: 0 0 0.3rem 0;
    border-bottom: 1px solid #3C3F41;
    padding-bottom: 0.2rem;
}

/* 区域内容样式 */
.area-content {
    font-size: calc(0.8rem + 0.2vw);
    line-height: 1.4;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-active { background-color: #4CAF50; }
.status-thinking { background-color: #FF9800; }
.status-converging { background-color: #2196F3; }
.status-completed { background-color: #9C27B0; }
.status-warning { background-color: #F44336; }

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #393B40;
    border-radius: 4px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.progress-fill {
    height: 100%;
    background-color: #0078D4;
    transition: width 0.3s ease;
}

/* 自动隐藏左侧菜单 */
.left-menu-trigger {
    position: fixed;
    left: 0;
    top: 0;
    width: 10px;
    height: 100vh;
    z-index: 999;
    background: transparent;
    cursor: pointer;
}

.left-menu {
    position: fixed;
    left: -250px;
    top: 0;
    width: 250px;
    height: 100vh;
    background: rgba(30, 31, 34, 0.95);
    backdrop-filter: blur(10px);
    border-right: 1px solid #3C3F41;
    z-index: 1000;
    transition: transform 0.3s ease;
    padding: 1rem;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

.left-menu.show {
    transform: translateX(250px);
}

.menu-item {
    display: block;
    color: #BBBBBB;
    text-decoration: none;
    padding: 0.8rem 1rem;
    margin: 0.2rem 0;
    border-radius: 4px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.menu-item:hover {
    background-color: #393B40;
    color: #0078D4;
    transform: translateX(5px);
}

.menu-title {
    color: #0078D4;
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #3C3F41;
}

/* VSCode风格滚动条 */
.vscode-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #424242 #1E1F22;
}

.vscode-scrollbar::-webkit-scrollbar {
    width: 14px;
}

.vscode-scrollbar::-webkit-scrollbar-track {
    background: #1E1F22;
}

.vscode-scrollbar::-webkit-scrollbar-thumb {
    background-color: #424242;
    border-radius: 0px;
    border: 3px solid #1E1F22;
}

.vscode-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #4F4F4F;
}

.vscode-scrollbar::-webkit-scrollbar-thumb:active {
    background-color: #6C6C6C;
}

/* 线框按钮hover效果 */
.control-buttons button:hover {
    opacity: 1 !important;
    background-color: rgba(255, 255, 255, 0.05) !important;
}

/* 基础按钮样式 */
.control-buttons {
    display: flex;
    gap: 0.3rem;
}

.control-buttons button {
    flex: 1;
    padding: 0.3rem;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
    font-size: 0.8rem;
}

.control-buttons button:hover {
    opacity: 1;
}

.control-buttons button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
} 