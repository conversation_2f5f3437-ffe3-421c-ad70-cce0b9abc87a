#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web界面应用 - 基于Flask的四重验证会议系统界面
引用: 00-共同配置.json 的 web_interface_config
"""

import sys
import os
from datetime import datetime
from flask import Flask, render_template, jsonify, request, redirect
from flask_socketio import SocketIO, emit

# 确保可以导入common模块
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..')
sys.path.insert(0, src_dir)

from unified_config_manager import UnifiedConfigManager
from common_error_handler import CommonErrorHandler

# 导入配置管理API
from configuration_center.web_api import config_api, set_config_center, setup_config_websocket

# 导入所有模块以实现完整功能
try:
    # 优先使用绝对导入
    from api_management.sqlite_storage.api_account_database import APIAccountDatabase, get_api_account_database
    from api_management.account_management.api_failover_manager import APIFailoverManager
    from api_management.account_management.unified_model_pool_butler import UnifiedModelPoolButler
    from bidirectional_collaboration.thinking_audit.thinking_quality_auditor import ThinkingQualityAuditor
    from bidirectional_collaboration.inspiration_extraction.algorithmic_insight_extractor import AlgorithmicInsightExtractor
    MODULES_AVAILABLE = True
except ImportError as e:
    try:
        # 降级到相对导入
        from ..api_management.sqlite_storage.api_account_database import APIAccountDatabase, get_api_account_database
        from ..api_management.account_management.api_failover_manager import APIFailoverManager
        from ..api_management.account_management.unified_model_pool_butler import UnifiedModelPoolButler
        from ..bidirectional_collaboration.thinking_audit.thinking_quality_auditor import ThinkingQualityAuditor
        from ..bidirectional_collaboration.inspiration_extraction.algorithmic_insight_extractor import AlgorithmicInsightExtractor
        MODULES_AVAILABLE = True
    except ImportError as e2:
        print(f"警告: 某些模块未能导入: {e2}")
        MODULES_AVAILABLE = False

from api_management.global_api_connection_pool import get_global_api_connection_pool

class WebInterfaceApp:
    """Web界面应用（基于Flask + SocketIO）

    V45容器化改造：支持多项目容器数据源
    """

    def __init__(self, project_manager=None, server_instance=None):
        # 使用UnifiedConfigManager替代CommonConfigLoader
        self.error_handler = CommonErrorHandler()
        self.web_config = UnifiedConfigManager.get_config('web_interface_config', {})

        # 🔧 边界清晰化：通过依赖注入使用项目管理器，移除本地存储
        self.project_manager = project_manager
        self.server_instance = server_instance  # IDEA式启动：服务器实例引用
        self.current_project = "default"
        self.container_data_source_enabled = bool(project_manager)

        # 初始化配置中心（使用UnifiedConfigManager）
        UnifiedConfigManager.initialize()
        set_config_center(None)  # 兼容性调用，实际使用UnifiedConfigManager

        # 初始化AI服务管理器单例（确保ValidationDrivenExecutor可以获取）
        self._initialize_ai_service_manager()

        # 初始化Flask应用
        self.app = Flask(__name__, static_folder='static', static_url_path='/static')
        self.app.config['SECRET_KEY'] = 'v4-four-layer-meeting-secret'

        # 禁用缓存 - 确保修改的JavaScript/CSS立即生效
        self.app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0

        self.socketio = SocketIO(self.app, cors_allowed_origins="*")

        # 设置SocketIO实例到配置API模块
        from configuration_center.web_api import set_socketio_instance
        set_socketio_instance(self.socketio)

        # 注册配置管理API蓝图
        self.app.register_blueprint(config_api)

        # 注册API管理蓝图

        # 设置配置WebSocket通知
        setup_config_websocket(self.socketio)

        # 注意：重启更新策略下，不再需要配置变更通知

        # 应用状态
        self.app_status = {
            "startup_time": datetime.now().isoformat(),
            "total_requests": 0,
            "active_sessions": 0,
            "system_health": "excellent",
            "container_mode": "V45_ENHANCED"  # V45容器化标识
        }

        # 初始化所有模块
        self._init_modules()

        # 注册路由
        self._register_routes()
        self._register_socketio_events()

        # 添加禁用缓存的响应头
        @self.app.after_request
        def after_request(response):
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
            return response

    # 注意：重启更新策略下，已移除配置变更通知功能
    # 配置变更需要重启应用以生效，通过重启确认对话框处理

    def _init_modules(self):
        """初始化所有功能模块"""
        if MODULES_AVAILABLE:
            try:
                # API管理模块（使用单例）
                self.api_db = get_api_account_database()
                self.failover_manager = APIFailoverManager(self.api_db)
                self.pool_butler = UnifiedModelPoolButler(self.api_db, self.failover_manager)

                # 双向协作模块
                self.thinking_auditor = ThinkingQualityAuditor()
                self.insight_extractor = AlgorithmicInsightExtractor()

                self.modules_status = {
                    "api_management": "active",
                    "failover_management": "active",
                    "pool_butler": "active",
                    "thinking_audit": "active",
                    "insight_extraction": "active"
                }

                print("✅ 所有功能模块初始化成功")

            except Exception as e:
                print(f"⚠️ 模块初始化部分失败: {e}")
                self.modules_status = {"error": str(e)}
        else:
            self.modules_status = {"status": "modules_unavailable"}

    def _register_routes(self):
        """注册Flask路由"""
        
        @self.app.route('/')
        def startup_cover():
            """IDEA式启动封面"""
            try:
                self.app_status["total_requests"] += 1
                return render_template('startup_cover.html',
                                     config=self.web_config,
                                     status=self.app_status)
            except Exception as e:
                # 如果startup_cover.html不存在，回退到九宫格界面
                print(f"⚠️ 启动封面模板未找到，回退到九宫格界面: {e}")
                return render_template('nine_grid.html',
                                     config=self.web_config,
                                     status=self.app_status)

        @self.app.route('/old-index')
        def old_index():
            """原始主页"""
            try:
                self.app_status["total_requests"] += 1
                return render_template('index.html',
                                     config=self.web_config,
                                     status=self.app_status)
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "原始主页加载"))

        @self.app.route('/debug')
        def debug_center():
            """调试中心"""
            try:
                self.app_status["total_requests"] += 1
                debug_info = self.error_handler.get_debug_info()
                return render_template('debug.html',
                                     debug_info=debug_info,
                                     status=self.app_status)
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "调试中心加载"))

        @self.app.route('/modules')
        def modules_center():
            """模块管理中心"""
            try:
                self.app_status["total_requests"] += 1
                return render_template('modules.html',
                                     modules_status=getattr(self, 'modules_status', {}),
                                     status=self.app_status)
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "模块中心加载"))

        @self.app.route('/nine-grid')
        def nine_grid():
            """九宫格界面 - 通用入口"""
            try:
                self.app_status["total_requests"] += 1
                return render_template('nine_grid.html',
                                     config=self.web_config,
                                     status=self.app_status)
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "九宫格界面加载"))

        @self.app.route('/pm_v2')
        def project_manager_v2():
            """V2项目经理工作台"""
            try:
                self.app_status["total_requests"] += 1
                return render_template('project_manager_v2.html',
                                     config=self.web_config,
                                     status=self.app_status)
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "V2项目经理工作台加载"))

        @self.app.route('/project/<project_name>/nine-grid')
        def project_nine_grid(project_name):
            """项目的九宫格界面 - IDEA式启动封面"""
            try:
                self.app_status["total_requests"] += 1

                # 获取项目配置
                project_config = None
                project_status_manager = getattr(self.project_manager, 'project_status_manager', None)
                if hasattr(self, 'server_instance') and hasattr(self.server_instance, 'project_status_manager'):
                    project_status_manager = self.server_instance.project_status_manager

                if project_status_manager:
                    project_config = project_status_manager.get_project_config(project_name)

                if not project_config:
                    # 项目不存在，返回首页
                    return redirect('/')

                return render_template('nine_grid.html',
                                     project=project_name,
                                     project_config=project_config,
                                     config=self.web_config,
                                     status=self.app_status)
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, f"项目{project_name}九宫格界面加载"))

        @self.app.route('/open_project/<project_name>')
        def open_project(project_name):
            """打开项目 - 从项目卡片点击进入"""
            try:
                # 检查项目是否存在
                if self.project_manager and self.project_manager.get_project_commander(project_name):
                    # 项目已启动，直接进入九宫格
                    return redirect(f'/project/{project_name}/nine-grid')
                else:
                    # 项目未启动，返回启动封面
                    return redirect('/')
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, f"打开项目{project_name}"))

        @self.app.route('/config')
        def config_center():
            """配置中心"""
            try:
                self.app_status["total_requests"] += 1
                return render_template('config_center.html',
                                     config=self.web_config,
                                     status=self.app_status)
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "配置中心加载"))

        @self.app.route('/test-nine-grid')
        def test_nine_grid():
            """测试九宫格界面"""
            return """
            <!DOCTYPE html>
            <html>
            <head><title>测试九宫格</title></head>
            <body>
                <h1>九宫格界面测试</h1>
                <p>如果看到这个页面，说明路由工作正常</p>
                <a href="/nine-grid">访问完整九宫格界面</a>
            </body>
            </html>
            """

        @self.app.route('/api/status')
        def api_status():
            """系统状态API - V45容器化改造"""
            try:
                # 🚀 V45容器化改造：获取容器化数据源
                container_status = self._get_container_dashboard_data() if self.container_data_source_enabled else {}

                return jsonify({
                    "status": "success",
                    "app_status": self.app_status,
                    "web_config": self.web_config,
                    "debug_url": self.web_config.get("debug_url"),
                    "timestamp": datetime.now().isoformat(),
                    # V45容器化数据
                    "container_mode": self.container_data_source_enabled,
                    "current_project": self.current_project,
                    "available_projects": self.project_manager.list_projects() if self.project_manager else [],
                    "container_status": container_status
                })
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "状态API"))

        @self.app.route('/api/project_cards')
        def get_project_cards():
            """获取项目卡片数据 - IDEA式启动封面"""
            try:
                # 获取项目状态管理器
                project_status_manager = getattr(self.project_manager, 'project_status_manager', None)
                if hasattr(self, 'server_instance') and hasattr(self.server_instance, 'project_status_manager'):
                    project_status_manager = self.server_instance.project_status_manager

                if project_status_manager:
                    cards = project_status_manager.get_project_cards()
                    return jsonify({
                        "status": "success",
                        "project_cards": cards,
                        "total_projects": len(cards)
                    })
                else:
                    return jsonify({
                        "status": "success",
                        "project_cards": [],
                        "total_projects": 0,
                        "message": "项目状态管理器未初始化"
                    })
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "获取项目卡片"))

        @self.app.route('/api/server_status')
        def get_server_status():
            """获取服务器状态 - IDEA式启动封面"""
            try:
                # 获取项目状态管理器
                project_status_manager = getattr(self.project_manager, 'project_status_manager', None)
                if hasattr(self, 'server_instance') and hasattr(self.server_instance, 'project_status_manager'):
                    project_status_manager = self.server_instance.project_status_manager

                if project_status_manager:
                    server_status = project_status_manager.get_server_status()
                    return jsonify({
                        "status": "success",
                        "server_status": server_status,
                        "timestamp": datetime.now().isoformat()
                    })
                else:
                    return jsonify({
                        "status": "success",
                        "server_status": {
                            "api_monitoring": {"error": "项目状态管理器未初始化"},
                            "primary_apis_available": "未知",
                            "backup_apis_available": "未知",
                            "overall_confidence": "0% (未初始化)",
                            "system_health": {"status": "warning", "icon": "⚠️", "text": "未初始化"},
                            "can_create_project": False
                        },
                        "timestamp": datetime.now().isoformat()
                    })
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "获取服务器状态"))

        @self.app.route('/api/system_metrics')
        def get_system_metrics():
            """获取系统指标 - 独立的系统监控API"""
            try:
                # 导入独立的系统监控模块
                from four_layer_meeting_server.system_monitor import get_system_metrics

                metrics = get_system_metrics()
                return jsonify({
                    "status": "success",
                    "metrics": metrics,
                    "timestamp": datetime.now().isoformat()
                })
            except ImportError as e:
                return jsonify({
                    "status": "error",
                    "message": f"系统监控模块导入失败: {str(e)}",
                    "metrics": None
                })
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "获取系统指标"))

        @self.app.route('/api/start_project', methods=['POST'])
        def api_start_project():
            """统一的项目启动API - IDEA式启动封面"""
            try:
                data = request.get_json()
                project_name = data.get('name')
                work_directory = data.get('workDirectory')
                description = data.get('description', '')

                if not project_name or not work_directory:
                    return jsonify({"success": False, "error": "项目名称和工作目录不能为空"})

                # 获取项目状态管理器
                project_status_manager = getattr(self.project_manager, 'project_status_manager', None)
                if hasattr(self, 'server_instance') and hasattr(self.server_instance, 'project_status_manager'):
                    project_status_manager = self.server_instance.project_status_manager

                if project_status_manager and self.project_manager:
                    # 启动项目指挥官（统一处理新建和打开）
                    commander = project_status_manager.start_project_commander(
                        project_name, work_directory, self.project_manager
                    )

                    if commander:
                        # 记录项目信息
                        project_info = {
                            "name": project_name,
                            "work_directory": work_directory,
                            "description": description,
                            "last_opened": datetime.now().isoformat()
                        }

                        project_status_manager.record_project_activity(project_name, project_info)

                        return jsonify({
                            "success": True,
                            "message": f"项目 {project_name} 启动成功",
                            "project_info": project_info
                        })
                    else:
                        return jsonify({"success": False, "error": "项目指挥官启动失败"})
                else:
                    return jsonify({"success": False, "error": "项目管理器或状态管理器未初始化"})

            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "启动项目API"))

        @self.app.route('/api/create_project', methods=['POST'])
        def api_create_project():
            """创建新项目API - 兼容性保留"""
            try:
                data = request.get_json()
                project_name = data.get('project_name')
                meeting_path = data.get('meeting_path')

                if not project_name:
                    return jsonify({"status": "error", "message": "项目名称不能为空"})

                if self.project_manager and self.project_manager.get_project(project_name):
                    return jsonify({"status": "error", "message": "项目已存在"})

                # 🔧 边界清晰化：实际调用服务器端的项目创建方法
                if self.project_manager:
                    container = self.project_manager.create_project(project_name, meeting_path)
                    if container:
                        return jsonify({
                            "status": "success",
                            "message": f"项目 {project_name} 创建成功",
                            "project_name": project_name,
                            "meeting_path": meeting_path
                        })
                    else:
                        return jsonify({"status": "error", "message": "项目创建失败"})
                else:
                    return jsonify({"status": "error", "message": "项目管理器未初始化"})

            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "创建项目API"))

        @self.app.route('/api/switch_project', methods=['POST'])
        def api_switch_project():
            """切换项目API - V45容器化改造"""
            try:
                data = request.get_json()
                project_name = data.get('project_name')

                if not project_name:
                    return jsonify({"status": "error", "message": "项目名称不能为空"})

                if not self.project_manager or not self.project_manager.get_project(project_name):
                    return jsonify({"status": "error", "message": "项目不存在"})

                # 切换当前项目
                success = self.switch_project(project_name)

                if success:
                    return jsonify({
                        "status": "success",
                        "message": f"已切换到项目: {project_name}",
                        "current_project": self.current_project
                    })
                else:
                    return jsonify({"status": "error", "message": "切换项目失败"})

            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "切换项目API"))

        @self.app.route('/api/health')
        def health_check():
            """健康检查API"""
            try:
                components = {
                    "web_interface": "active",
                    "debug_center": "active",
                    "socketio": "active"
                }

                # 添加模块状态
                if hasattr(self, 'modules_status'):
                    components.update(self.modules_status)

                return jsonify({
                    "status": "healthy",
                    "uptime": datetime.now().isoformat(),
                    "version": "v2.0-dry-simplified",
                    "components": components
                })
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "健康检查"))

        @self.app.route('/api/logs/status')
        def logs_status():
            """日志状态API - 获取真实的日志统计信息"""
            try:
                # 获取真实的日志统计信息
                log_stats = self._get_real_log_statistics()
                return jsonify({
                    "success": True,
                    "status": log_stats,
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as e:
                return jsonify({
                    "success": False,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })

        @self.app.route('/api/logs/files')
        def logs_files():
            """日志文件列表API - 获取真实的日志文件信息"""
            try:
                files_info = self._get_real_log_files()
                return jsonify({
                    "success": True,
                    "files": files_info,
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as e:
                return jsonify({
                    "success": False,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })

        @self.app.route('/api/logs/cleanup', methods=['POST'])
        def logs_cleanup():
            """日志清理API - 执行真实的日志清理"""
            try:
                cleanup_result = self._cleanup_real_logs()
                return jsonify({
                    "success": True,
                    "message": cleanup_result,
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as e:
                return jsonify({
                    "success": False,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })

        @self.app.route('/api/logs/export', methods=['POST'])
        def logs_export():
            """日志导出API - 导出真实的日志文件"""
            try:
                export_result = self._export_real_logs()
                return jsonify({
                    "success": True,
                    "download_url": export_result["url"],
                    "filename": export_result["filename"],
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as e:
                return jsonify({
                    "success": False,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })

        @self.app.route('/exports/<filename>')
        def download_export(filename):
            """下载导出文件"""
            try:
                from flask import send_from_directory
                return send_from_directory('exports', filename, as_attachment=True)
            except Exception as e:
                return jsonify({"error": f"文件下载失败: {str(e)}"}), 404

        @self.app.route('/api/modules/status')
        def modules_status():
            """模块状态API"""
            try:
                if not MODULES_AVAILABLE:
                    return jsonify({"status": "error", "message": "模块不可用"})

                # 获取池状态（同步方式）
                pool_status = {}
                if hasattr(self, 'pool_butler'):
                    try:
                        # 使用同步方法获取状态
                        pool_status = {
                            "active_requests": len(getattr(self.pool_butler, 'active_requests', {})),
                            "max_concurrent": getattr(self.pool_butler, 'max_concurrent_requests', 5),
                            "statistics": getattr(self.pool_butler, 'stats', {}),
                            "last_updated": datetime.now().isoformat()
                        }
                    except Exception as e:
                        pool_status = {"error": str(e)}

                status_data = {
                    "modules": self.modules_status,
                    "api_status": self.failover_manager.get_current_api_status() if hasattr(self, 'failover_manager') else {},
                    "pool_status": pool_status,
                    "audit_summary": self.thinking_auditor.get_audit_summary() if hasattr(self, 'thinking_auditor') else {},
                    "evolution_summary": self.insight_extractor.get_evolution_summary() if hasattr(self, 'insight_extractor') else {},
                    "timestamp": datetime.now().isoformat()
                }

                return jsonify({"status": "success", "data": status_data})
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "模块状态获取"))

        @self.app.route('/api/thinking/audit', methods=['POST'])
        def thinking_audit():
            """思维质量审查API"""
            try:
                if not hasattr(self, 'thinking_auditor'):
                    return jsonify({"status": "error", "message": "思维审查模块不可用"})

                data = request.get_json()
                thinking_content = data.get('thinking_content', '')
                task_context = data.get('task_context', '')

                if not thinking_content:
                    return jsonify({"status": "error", "message": "思维内容不能为空"})

                audit_result = self.thinking_auditor.audit_thinking_process(thinking_content, task_context)
                return jsonify({"status": "success", "data": audit_result})

            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "思维审查"))

        @self.app.route('/api/insights/extract', methods=['POST'])
        def insights_extract():
            """算法启发提取API"""
            try:
                if not hasattr(self, 'insight_extractor'):
                    return jsonify({"status": "error", "message": "启发提取模块不可用"})

                data = request.get_json()
                thinking_content = data.get('thinking_content', '')
                execution_context = data.get('execution_context', '')

                if not thinking_content:
                    return jsonify({"status": "error", "message": "思维内容不能为空"})

                extraction_result = self.insight_extractor.extract_algorithmic_insights(thinking_content, execution_context)
                return jsonify({"status": "success", "data": extraction_result})

            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "启发提取"))

        # V4.5革命性升级 - 新增API接口
        @self.app.route('/api/strategy-routes')
        def strategy_routes():
            """25条策略路线状态API"""
            try:
                import random

                # 模拟25条策略路线状态
                routes = [
                    # 传统策略路线(14条)
                    {"name": "包围反推", "type": "traditional", "status": "active", "confidence": 85},
                    {"name": "边界中心", "type": "traditional", "status": "executing", "confidence": 92},
                    {"name": "分治算法", "type": "traditional", "status": "active", "confidence": 88},
                    {"name": "约束传播", "type": "traditional", "status": "standby", "confidence": 0},
                    {"name": "演绎归纳", "type": "traditional", "status": "active", "confidence": 79},
                    {"name": "契约设计", "type": "traditional", "status": "standby", "confidence": 0},
                    {"name": "不变式验证", "type": "traditional", "status": "active", "confidence": 91},
                    {"name": "边界值分析", "type": "traditional", "status": "standby", "confidence": 0},
                    {"name": "状态机验证", "type": "traditional", "status": "standby", "confidence": 0},
                    {"name": "形式化建模", "type": "traditional", "status": "standby", "confidence": 0},
                    {"name": "溯因推理", "type": "traditional", "status": "standby", "confidence": 0},
                    {"name": "假设验证", "type": "traditional", "status": "standby", "confidence": 0},
                    {"name": "反证法", "type": "traditional", "status": "standby", "confidence": 0},
                    {"name": "归纳法", "type": "traditional", "status": "standby", "confidence": 0},

                    # ACE增强路线(5条)
                    {"name": "动态状态感知", "type": "ace", "status": "executing", "confidence": 94},
                    {"name": "语义歧义消解", "type": "ace", "status": "active", "confidence": 87},
                    {"name": "跨文档验证", "type": "ace", "status": "standby", "confidence": 0},
                    {"name": "认知负荷管理", "type": "ace", "status": "active", "confidence": 83},
                    {"name": "智能涌现检测", "type": "ace", "status": "executing", "confidence": 96},

                    # 终极策略路线(6条)
                    {"name": "五维融合验证", "type": "ultimate", "status": "active", "confidence": 98},
                    {"name": "实时自适应优化", "type": "ultimate", "status": "executing", "confidence": 95},
                    {"name": "元策略决策", "type": "ultimate", "status": "active", "confidence": 89},
                    {"name": "突破性验证", "type": "ultimate", "status": "standby", "confidence": 0},
                    {"name": "质量收敛算法", "type": "ultimate", "status": "active", "confidence": 92},
                    {"name": "零矛盾追求", "type": "ultimate", "status": "executing", "confidence": 97}
                ]

                # 添加一些随机变化
                for route in routes:
                    if route["status"] != "standby":
                        route["confidence"] += random.randint(-5, 5)
                        route["confidence"] = max(0, min(100, route["confidence"]))

                return jsonify({
                    "status": "success",
                    "data": {
                        "routes": routes,
                        "total_active": len([r for r in routes if r["status"] == "active"]),
                        "total_executing": len([r for r in routes if r["status"] == "executing"]),
                        "average_confidence": sum(r["confidence"] for r in routes if r["status"] != "standby") / len([r for r in routes if r["status"] != "standby"]),
                        "timestamp": datetime.now().isoformat()
                    }
                })
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "策略路线状态"))

        @self.app.route('/api/cognitive-load')
        def cognitive_load():
            """认知负荷监控API"""
            try:
                import random

                # 模拟4AI认知负荷数据
                loads = {
                    "ide_ai": {
                        "current_load": random.randint(30, 60),
                        "memory_usage": random.randint(40, 70),
                        "task_complexity": random.randint(3, 7),
                        "status": "normal"
                    },
                    "python_ai_1": {
                        "current_load": random.randint(50, 80),
                        "memory_usage": random.randint(60, 85),
                        "task_complexity": random.randint(5, 9),
                        "status": "warning" if random.random() > 0.7 else "normal"
                    },
                    "python_ai_2": {
                        "current_load": random.randint(40, 70),
                        "memory_usage": random.randint(50, 75),
                        "task_complexity": random.randint(4, 8),
                        "status": "normal"
                    },
                    "python_ai_3": {
                        "current_load": random.randint(70, 95),
                        "memory_usage": random.randint(75, 95),
                        "task_complexity": random.randint(6, 10),
                        "status": "danger" if random.random() > 0.6 else "warning"
                    }
                }

                # 计算整体指标
                avg_load = sum(ai["current_load"] for ai in loads.values()) / len(loads)
                max_load = max(ai["current_load"] for ai in loads.values())

                # 幻觉风险评估
                hallucination_risk = min(0.5, max_load / 200 + random.uniform(-0.05, 0.05))

                return jsonify({
                    "status": "success",
                    "data": {
                        "ai_loads": loads,
                        "overall_metrics": {
                            "average_load": round(avg_load, 1),
                            "max_load": max_load,
                            "memory_boundary_warning": max_load >= 70,
                            "hallucination_risk": round(hallucination_risk, 3),
                            "risk_level": "high" if hallucination_risk >= 0.4 else "medium" if hallucination_risk >= 0.2 else "low"
                        },
                        "timestamp": datetime.now().isoformat()
                    }
                })
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "认知负荷监控"))

        @self.app.route('/api/keys/update', methods=['POST'])
        def update_api_key():
            """更新API密钥（重构版 - 通过ApiMemoryManager接口）"""
            try:
                # 🔥 重构：使用ApiMemoryManager替代直接数据库访问
                try:
                    from api_management.core.api_memory_manager import get_api_memory_manager
                    api_memory_manager = get_api_memory_manager()
                except ImportError:
                    return jsonify({"success": False, "error": "ApiMemoryManager不可用"})

                data = request.get_json()
                provider = data.get('provider')
                api_key = data.get('api_key')

                if not provider or not api_key:
                    return jsonify({"success": False, "error": "缺少必要参数"})

                # 基于统一配置管理的动态厂商映射
                provider_mapping = self._get_dynamic_provider_mapping()

                # 获取提供商配置
                provider_config = provider_mapping.get(provider)
                if not provider_config:
                    return jsonify({"success": False, "error": f"未知的提供商: {provider}"})

                # 🔥 重构：通过ApiMemoryManager存储API配置
                config_data = {
                    'model_name': provider_config['model_name'],
                    'role': provider_config['role'],
                    'api_type': provider_config['api_type'],
                    'provider': provider,
                    'updated_at': datetime.now().isoformat()
                }

                # 使用ApiMemoryManager添加API配置
                success = api_memory_manager.add_api_config(api_key, config_data)

                if success:
                    return jsonify({
                        "success": True,
                        "message": f"API密钥已更新: {provider}",
                        "provider": provider,
                        "model_name": provider_config['model_name']
                    })
                else:
                    return jsonify({
                        "success": False,
                        "error": "API配置添加失败"
                    })

            except Exception as e:
                return jsonify({"success": False, "error": str(e)})

        def _get_dynamic_provider_mapping(self) -> dict:
            """基于统一配置管理获取动态厂商映射"""
            try:
                from utils.name_utils import get_provider_configs, get_display_name

                # 获取所有厂商配置
                all_configs = get_provider_configs()
                provider_mapping = {}

                # 为每个标准配置创建厂商映射条目
                for provider, configs in all_configs.items():
                    for standard_name, config in configs.items():
                        # 生成厂商键名（兼容现有前端）
                        provider_key = self._generate_provider_key(standard_name, config)

                        provider_mapping[provider_key] = {
                            'model_name': config.get('model_name', standard_name),
                            'role': config.get('role', get_display_name(standard_name=standard_name)),
                            'api_type': self._determine_api_type(standard_name, config),
                            'standard_name': standard_name,
                            'provider': provider
                        }

                return provider_mapping

            except Exception as e:
                print(f"❌ 获取动态厂商映射失败: {e}")
                # 后备：返回基本映射
                return {
                    'deepseek_r1': {
                        'model_name': 'deepseek-ai/DeepSeek-R1',
                        'role': '架构专家',
                        'api_type': 'primary'
                    }
                }

        def _generate_provider_key(self, standard_name: str, config: dict) -> str:
            """生成厂商键名（保持前端兼容性）"""
            # 基于标准名称生成键名
            if 'deepseek' in standard_name.lower():
                if 'r1' in standard_name.lower():
                    if '0528' in standard_name.lower():
                        return 'gmi_deepseek_r1'  # 兼容现有前端
                    else:
                        return 'chutes_deepseek_r1'
                elif 'v3' in standard_name.lower():
                    return 'gmi_deepseek_v3'
            elif 'deepcoder' in standard_name.lower():
                return 'chutes_deepcoder_14b'
            elif 'gemini' in standard_name.lower():
                return 'gmi_gemini_pro'

            # 默认：使用标准名称
            return standard_name

        def _determine_api_type(self, standard_name: str, config: dict) -> str:
            """确定API类型"""
            # 从配置中获取API类型
            api_type = config.get('api_type')
            if api_type:
                return api_type

            # 基于角色推断API类型
            role = config.get('role', '').lower()
            if '备用' in role or 'backup' in role:
                return 'backup'
            else:
                return 'primary'

        @self.app.route('/api/keys/status')
        def get_api_key_status():
            """获取API密钥状态（重构版 - 通过ApiMemoryManager接口）"""
            try:
                # 🔥 重构：使用ApiMemoryManager替代直接数据库访问
                try:
                    from api_management.core.api_memory_manager import get_api_memory_manager
                    api_memory_manager = get_api_memory_manager()
                except ImportError:
                    return jsonify({"success": False, "error": "ApiMemoryManager不可用"})

                # 获取所有API配置状态
                status = {}

                # 基于动态配置获取提供商列表
                provider_mapping = self._get_dynamic_provider_mapping()
                providers = list(provider_mapping.keys())

                # 提供商到模型名称的映射（动态生成）
                provider_to_model = {
                    provider: config['model_name']
                    for provider, config in provider_mapping.items()
                }

                for provider in providers:
                    try:
                        # 🔥 重构：通过ApiMemoryManager获取质量分数列表
                        model_name = provider_to_model.get(provider, provider)

                        # 查找匹配的API配置
                        quality_scores = api_memory_manager.list_quality_scores({
                            'model_name': model_name
                        })

                        if not quality_scores:
                            # 尝试按provider查找
                            quality_scores = api_memory_manager.list_quality_scores({
                                'provider': provider
                            })

                        if quality_scores:
                            # 检查是否有正常状态的API
                            active_apis = [api for api in quality_scores if api.get('status') == 'normal']
                            if active_apis:
                                status[provider] = 'active'
                            else:
                                status[provider] = 'inactive'
                        else:
                            status[provider] = 'inactive'

                    except Exception as e:
                        print(f"⚠️ 检查提供商状态失败 {provider}: {e}")
                        status[provider] = 'unknown'

                return jsonify({
                    "success": True,
                    "status": status
                })

            except Exception as e:
                return jsonify({"success": False, "error": str(e)})

        @self.app.route('/api/keys/test-all', methods=['POST'])
        def test_all_api_keys():
            """测试所有API密钥（重构版 - 通过ApiMemoryManager接口）"""
            try:
                # 🔥 重构：使用ApiMemoryManager替代直接数据库访问
                try:
                    from api_management.core.api_memory_manager import get_api_memory_manager
                    api_memory_manager = get_api_memory_manager()
                except ImportError:
                    return jsonify({"success": False, "error": "ApiMemoryManager不可用"})

                results = {}
                # 基于动态配置获取提供商列表
                provider_mapping = self._get_dynamic_provider_mapping()
                providers = list(provider_mapping.keys())

                for provider in providers:
                    try:
                        # 🔥 重构：通过ApiMemoryManager获取API配置
                        provider_to_model = {
                            provider: config['model_name']
                            for provider, config in provider_mapping.items()
                        }
                        model_name = provider_to_model.get(provider, provider)

                        # 查找匹配的API
                        quality_scores = api_memory_manager.list_quality_scores({
                            'model_name': model_name
                        })

                        if not quality_scores:
                            # 尝试按provider查找
                            quality_scores = api_memory_manager.list_quality_scores({
                                'provider': provider
                            })

                        if quality_scores:
                            # 检查API状态和质量
                            normal_apis = [api for api in quality_scores if api.get('status') == 'normal']
                            if normal_apis:
                                avg_quality = sum(api.get('quality_score', 0) for api in normal_apis) / len(normal_apis)
                                results[provider] = {
                                    'status': True,
                                    'message': f'API密钥已配置，平均质量分数: {avg_quality:.1f}',
                                    'api_count': len(normal_apis),
                                    'average_quality': avg_quality,
                                    'last_test': datetime.now().isoformat()
                                }
                            else:
                                results[provider] = {
                                    'status': False,
                                    'message': 'API密钥已配置但状态异常',
                                    'api_count': len(quality_scores),
                                    'last_test': datetime.now().isoformat()
                                }
                        else:
                            results[provider] = {
                                'status': False,
                                'message': 'API密钥未配置',
                                'last_test': datetime.now().isoformat()
                            }

                    except Exception as e:
                        results[provider] = {
                            'status': False,
                            'message': f'测试失败: {str(e)}',
                            'last_test': datetime.now().isoformat()
                        }

                return jsonify({
                    "success": True,
                    "results": results
                })

            except Exception as e:
                return jsonify({"success": False, "error": str(e)})

        @self.app.route('/api/ai/request', methods=['POST'])
        def request_ai_assistance():
            """AI协助请求API - V45容器架构集成"""
            try:
                data = request.get_json()

                # 🚀 V45容器架构调用（优先）
                if self.container_data_source_enabled and hasattr(self, 'project_manager'):
                    # 通过容器调用API管理器
                    try:
                        container = self.project_manager.get_current_container()
                        if container:
                            result = container.component_call(
                                "web_interface", "api_manager", "request_ai_assistance", data
                            )

                            if result and not result.get("error"):
                                return jsonify({
                                    "success": True,
                                    "data": result,
                                    "call_method": "container",
                                    "timestamp": datetime.now().isoformat()
                                })
                            else:
                                print(f"⚠️ 容器调用失败: {result.get('error', '未知错误')}")
                    except Exception as e:
                        print(f"⚠️ 容器调用异常: {e}")

                # 🚀 直接调用新API管理器（备用）
                try:
                    from api_management.core import get_task_based_ai_service, initialize_api_manager_core

                    if initialize_api_manager_core():
                        api_service = get_task_based_ai_service()

                        # 异步调用处理
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        try:
                            result = loop.run_until_complete(
                                api_service.request_ai_assistance(**data)
                            )

                            return jsonify({
                                "success": True,
                                "data": result,
                                "call_method": "direct_api_manager",
                                "timestamp": datetime.now().isoformat()
                            })
                        finally:
                            loop.close()
                    else:
                        raise ImportError("API管理器初始化失败")

                except ImportError as e:
                    print(f"⚠️ 新API管理器不可用: {e}")

                # 🚀 向后兼容旧API调用（兜底）
                if hasattr(self, 'pool_butler'):
                    try:
                        # 构造旧格式的请求
                        legacy_request = {
                            "api_role": data.get("assistance_type", "code_generation"),
                            "data": {
                                "task": data.get("task_description", ""),
                                "context": data.get("context_info", {}),
                                "complexity": data.get("complexity_level", "medium"),
                                "priority": data.get("priority", "normal")
                            }
                        }

                        # 同步调用旧API
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        try:
                            result = loop.run_until_complete(
                                self.pool_butler.execute_api_request(legacy_request)
                            )

                            if result and result.get("status") == "success":
                                # 转换为新格式
                                return jsonify({
                                    "success": True,
                                    "data": {
                                        "success": True,
                                        "content": result.get("result", {}).get("response", ""),
                                        "task_category": data.get("task_category") or data.get("assistance_type", "text_processing"),
                                        "capability_used": "legacy_api",
                                        "metadata": {
                                            "legacy_mode": True,
                                            "original_result": result
                                        }
                                    },
                                    "call_method": "legacy_api",
                                    "timestamp": datetime.now().isoformat()
                                })
                        finally:
                            loop.close()
                    except Exception as e:
                        print(f"⚠️ 向后兼容API调用失败: {e}")

                # 所有方法都失败
                return jsonify({
                    "success": False,
                    "error": "所有AI服务调用方法都不可用",
                    "available_methods": {
                        "container_call": self.container_data_source_enabled,
                        "direct_api_manager": False,
                        "legacy_api": hasattr(self, 'pool_butler')
                    },
                    "timestamp": datetime.now().isoformat()
                }), 503

            except Exception as e:
                return jsonify({
                    "success": False,
                    "error": f"AI请求处理异常: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                }), 500

            except Exception as e:
                return jsonify({"success": False, "error": str(e)})

        @self.app.route('/api/intelligence-emergence')
        def intelligence_emergence():
            """智能涌现检测API"""
            try:
                import random

                # 模拟智能涌现数据
                emergence_score = random.randint(80, 100)

                # 涌现事件历史
                events = [
                    {
                        "type": "breakthrough",
                        "description": "五维融合达到98%一致性",
                        "quality_score": 98,
                        "timestamp": "14:15:23"
                    },
                    {
                        "type": "emergence",
                        "description": "元策略自动优化发现",
                        "quality_score": 92,
                        "timestamp": "14:12:45"
                    },
                    {
                        "type": "quality_jump",
                        "description": "零矛盾状态持续3分钟",
                        "quality_score": 96,
                        "timestamp": "14:08:12"
                    }
                ]

                # 五维协同状态
                five_dimensions = {
                    "rules": random.randint(85, 98),
                    "algorithms": random.randint(88, 96),
                    "ai": random.randint(82, 94),
                    "cognition": random.randint(79, 92),
                    "evolution": random.randint(86, 99)
                }

                return jsonify({
                    "status": "success",
                    "data": {
                        "emergence_score": emergence_score,
                        "quality_level": "breakthrough" if emergence_score >= 95 else "high" if emergence_score >= 90 else "medium",
                        "five_dimensions": five_dimensions,
                        "recent_events": events,
                        "prediction": {
                            "next_emergence_probability": random.randint(60, 90),
                            "estimated_time": f"{random.randint(2, 8)}分钟"
                        },
                        "timestamp": datetime.now().isoformat()
                    }
                })
            except Exception as e:
                return jsonify(self.error_handler.web_error_response(e, "智能涌现检测"))

        # Universal Debugger API端点
        @self.app.route('/api/universal_debugger', methods=['POST'])
        def universal_debugger():
            """Universal Debugger API - JS直接调用，动态创建执行器"""
            import asyncio
            try:
                # 导入Universal Debugger
                from debug.universal_debugger import debug_from_js

                data = request.get_json()

                # 创建新的事件循环（如果当前没有运行的循环）
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # 如果已有运行的循环，使用run_in_executor
                        import concurrent.futures
                        with concurrent.futures.ThreadPoolExecutor() as executor_pool:
                            future = executor_pool.submit(
                                asyncio.run,
                                debug_from_js(data)
                            )
                            # 增加超时时间到10分钟，避免长时间AI调用超时
                            result = future.result(timeout=600)  # 10分钟超时
                    else:
                        # 如果没有运行的循环，直接运行
                        result = asyncio.run(debug_from_js(data))
                except RuntimeError:
                    # 如果获取事件循环失败，创建新的
                    result = asyncio.run(debug_from_js(data))
                except concurrent.futures.TimeoutError:
                    return jsonify({
                        "success": False,
                        "error": "执行超时（10分钟），请检查AI服务是否正常",
                        "error_type": "TimeoutError",
                        "suggestion": "AI调用可能需要更长时间，请稍后重试或检查网络连接"
                    })

                return jsonify(result)

            except ImportError as e:
                return jsonify({
                    "success": False,
                    "error": f"Universal Debugger模块导入失败: {str(e)}",
                    "error_type": "ImportError",
                    "suggestion": "请确保debug模块已正确安装"
                })
            except Exception as e:
                import traceback
                return jsonify({
                    "success": False,
                    "error": f"Universal Debugger执行失败: {str(e)}",
                    "error_type": type(e).__name__,
                    "detailed_error": traceback.format_exc()
                })

    def _register_socketio_events(self):
        """注册SocketIO事件"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """客户端连接"""
            self.app_status["active_sessions"] += 1
            emit('status_update', {
                "message": "连接成功",
                "active_sessions": self.app_status["active_sessions"],
                "timestamp": datetime.now().isoformat()
            })

        @self.socketio.on('disconnect')
        def handle_disconnect():
            """客户端断开连接"""
            self.app_status["active_sessions"] = max(0, self.app_status["active_sessions"] - 1)

        @self.socketio.on('request_status')
        def handle_status_request():
            """处理状态请求"""
            emit('status_response', {
                "app_status": self.app_status,
                "timestamp": datetime.now().isoformat()
            })

        @self.socketio.on('debug_request')
        def handle_debug_request(data):
            """处理调试请求"""
            try:
                debug_info = self.error_handler.get_debug_info()
                emit('debug_response', {
                    "debug_info": debug_info,
                    "request_data": data,
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as e:
                emit('debug_error', self.error_handler.web_error_response(e, "调试请求处理"))

        @self.socketio.on('debug_command')
        def handle_debug_command(data):
            """处理调试命令 - 发送到服务器端debug_log"""
            try:
                command = data.get('command', '')
                # 通过SocketIO发送debug_log_update事件
                emit('debug_log_update', {
                    "timestamp": datetime.now().isoformat(),
                    "level": "DEBUG",
                    "source": "USER",
                    "message": f"执行命令: {command}"
                })
            except Exception as e:
                emit('debug_error', self.error_handler.web_error_response(e, "调试命令处理"))

        @self.socketio.on('meeting_control')
        def handle_meeting_control(data):
            """处理会议控制请求"""
            try:
                action = data.get('action', '')
                print(f"会议控制: {action}")  # 临时使用print，实际应该通过Web界面显示

                emit('meeting_control_response', {
                    "action": action,
                    "status": "success",
                    "message": f"会议{action}操作已执行",
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as e:
                emit('meeting_control_error', self.error_handler.web_error_response(e, "会议控制"))

        @self.socketio.on('human_choice')
        def handle_human_choice(data):
            """处理人类选择题回答"""
            try:
                choice = data.get('choice', '')
                print(f"人类选择: {choice}")  # 临时使用print，实际应该通过Web界面显示

                emit('human_choice_response', {
                    "choice": choice,
                    "status": "success",
                    "message": f"选择{choice}已记录",
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as e:
                emit('human_choice_error', self.error_handler.web_error_response(e, "人类选择处理"))



    def get_app_info(self):
        """获取应用信息"""
        return {
            "app_name": "四重验证会议系统Web界面",
            "version": "v2.0-dry-simplified",
            "status": self.app_status,
            "config": self.web_config,
            "debug_available": True,
            "socketio_enabled": True
        }

    def _get_real_log_statistics(self):
        """获取真实的日志统计信息"""
        import os

        log_stats = {
            "algorithm_thinking": self._get_log_type_stats("Meeting/algorithm_thinking_logs", "thinking_log"),
            "ai_communication": self._get_log_type_stats("Meeting/ai_communication_logs", "ai_comm_log"),
            "python_algorithm_operations": self._get_log_type_stats("Meeting/python_algorithm_operations_logs", "py_ops_log"),
            "total_size_mb": 0
        }

        # 计算总大小
        total_size = sum(stats["size_mb"] for stats in log_stats.values() if isinstance(stats, dict))
        log_stats["total_size_mb"] = round(total_size, 1)

        return log_stats

    def _get_log_type_stats(self, log_dir, file_prefix):
        """获取特定日志类型的统计信息"""
        import os

        if not os.path.exists(log_dir):
            return {"file_count": 0, "size_mb": 0, "max_size_mb": 50}

        files = [f for f in os.listdir(log_dir) if f.startswith(file_prefix) and f.endswith('.jsonl')]
        total_size = 0

        for filename in files:
            filepath = os.path.join(log_dir, filename)
            try:
                total_size += os.path.getsize(filepath)
            except OSError:
                continue

        # 根据日志类型设置最大大小限制
        max_size_map = {
            "thinking_log": 50,
            "ai_comm_log": 20,
            "py_ops_log": 30
        }
        max_size_mb = max_size_map.get(file_prefix, 50)

        # 更精确的大小显示
        if total_size < 1024:
            size_display = f"{total_size}B"
        elif total_size < 1024 * 1024:
            size_display = f"{round(total_size / 1024, 1)}KB"
        else:
            size_display = f"{round(total_size / (1024 * 1024), 1)}MB"

        return {
            "file_count": len(files),
            "size_mb": round(total_size / (1024 * 1024), 1),
            "size_display": size_display,
            "max_size_mb": max_size_mb
        }

    def _get_real_log_files(self):
        """获取真实的日志文件信息"""
        import os
        from datetime import datetime

        files_info = {}
        log_dirs = {
            "algorithm_thinking": ("Meeting/algorithm_thinking_logs", "thinking_log"),
            "ai_communication": ("Meeting/ai_communication_logs", "ai_comm_log"),
            "python_algorithm_operations": ("Meeting/python_algorithm_operations_logs", "py_ops_log")
        }

        for log_type, (log_dir, file_prefix) in log_dirs.items():
            files_info[log_type] = []

            if not os.path.exists(log_dir):
                continue

            for filename in os.listdir(log_dir):
                if filename.startswith(file_prefix) and filename.endswith('.jsonl'):
                    filepath = os.path.join(log_dir, filename)
                    try:
                        stat = os.stat(filepath)
                        files_info[log_type].append({
                            "filename": filename,
                            "size_kb": round(stat.st_size / 1024, 1),
                            "modified_time": datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
                        })
                    except OSError:
                        continue

        return files_info

    def _cleanup_real_logs(self):
        """执行真实的日志清理"""
        import os

        cleanup_results = []
        log_dirs = {
            "algorithm_thinking": ("Meeting/algorithm_thinking_logs", "thinking_log", 10),
            "ai_communication": ("Meeting/ai_communication_logs", "ai_comm_log", 4),
            "python_algorithm_operations": ("Meeting/python_algorithm_operations_logs", "py_ops_log", 4)
        }

        for log_type, (log_dir, file_prefix, max_files) in log_dirs.items():
            if not os.path.exists(log_dir):
                continue

            files = []
            for filename in os.listdir(log_dir):
                if filename.startswith(file_prefix) and filename.endswith('.jsonl'):
                    filepath = os.path.join(log_dir, filename)
                    try:
                        mtime = os.path.getmtime(filepath)
                        files.append((filepath, mtime))
                    except OSError:
                        continue

            # 按修改时间排序，最新的在前
            files.sort(key=lambda x: x[1], reverse=True)

            # 删除超出限制的文件
            deleted_count = 0
            if len(files) > max_files:
                for filepath, _ in files[max_files:]:
                    try:
                        os.remove(filepath)
                        deleted_count += 1
                        print(f"🗑️ 删除日志文件: {os.path.basename(filepath)}")
                    except OSError:
                        continue

            if deleted_count > 0:
                cleanup_results.append(f"{log_type}: 删除了 {deleted_count} 个文件")
            else:
                # 即使没有删除文件，也提供有用的信息
                cleanup_results.append(f"{log_type}: {len(files)}个文件，未超出限制({max_files}个)")

        if cleanup_results:
            return "; ".join(cleanup_results)
        else:
            return "没有找到日志文件"

    def _export_real_logs(self):
        """导出真实的日志文件"""
        import os
        import zipfile
        from datetime import datetime

        # 创建导出目录
        export_dir = "exports"
        os.makedirs(export_dir, exist_ok=True)

        # 生成导出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        zip_filename = f"logs_export_{timestamp}.zip"
        zip_filepath = os.path.join(export_dir, zip_filename)

        # 创建ZIP文件
        with zipfile.ZipFile(zip_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
            log_dirs = [
                "Meeting/algorithm_thinking_logs",
                "Meeting/ai_communication_logs",
                "Meeting/python_algorithm_operations_logs"
            ]

            for log_dir in log_dirs:
                if os.path.exists(log_dir):
                    for filename in os.listdir(log_dir):
                        if filename.endswith('.jsonl'):
                            filepath = os.path.join(log_dir, filename)
                            arcname = os.path.join(os.path.basename(log_dir), filename)
                            zipf.write(filepath, arcname)

        return {
            "url": f"/exports/{zip_filename}",
            "filename": zip_filename
        }

    def run(self, host=None, port=None, debug=False):
        """启动Web应用"""
        try:
            # 使用传入的参数或配置文件中的默认值
            run_host = host or self.web_config['host']
            run_port = port or self.web_config['port']

            print(f"🌐 启动Web界面服务器...")
            print(f"📍 访问地址: http://{run_host}:{run_port}")
            print(f"🐛 调试地址: http://{run_host}:{run_port}/debug")
            print(f"🔧 并发模式: 多线程支持已启用")

            self.socketio.run(
                self.app,
                host=run_host,
                port=run_port,
                debug=debug,
                allow_unsafe_werkzeug=True,
                use_reloader=False  # 禁用自动重载，避免重复启动
            )
        except Exception as e:
            print(f"❌ Web服务器启动失败: {e}")
            raise

    # ==================== V45容器化改造方法 ====================

    def register_project_container(self, project_name: str, container):
        """注册项目容器 - V45容器化改造（已废弃，保留兼容性）"""
        # 🔧 边界清晰化：此方法已废弃，项目管理统一由ProjectManager处理
        print(f"⚠️ register_project_container已废弃，项目管理由ProjectManager统一处理")
        if not self.container_data_source_enabled:
            self.container_data_source_enabled = True
            self.current_project = project_name

    def switch_project(self, project_name: str):
        """切换当前项目 - V45容器化改造"""
        if self.project_manager and self.project_manager.get_project(project_name):
            self.current_project = project_name
            print(f"✅ Web界面切换到项目: {project_name}")
            return True
        else:
            print(f"❌ 项目不存在: {project_name}")
            return False

    def _get_container_dashboard_data(self):
        """获取容器化仪表板数据 - V45容器化改造"""
        if not self.container_data_source_enabled or not self.project_manager:
            return {}

        try:
            container = self.project_manager.get_project(self.current_project)
            if not container:
                return {}

            # 获取容器状态数据
            dashboard_data = {
                "project_name": self.current_project,
                "container_id": container.container_id,
                "components": container.get_all_component_states(),
                "system_metrics": {
                    "total_components": len(container.components),
                    "active_components": len([c for c in container.components.values() if c.get("status") == "ready"]),
                    "total_calls": container.get_state("system", "total_calls") or 0,
                    "last_activity": container.get_state("system", "last_activity")
                },
                "recent_activities": container.get_state("system", "recent_activities") or []
            }

            return dashboard_data

        except Exception as e:
            print(f"❌ 获取容器仪表板数据失败: {e}")
            return {"error": str(e)}

    def _initialize_ai_service_manager(self):
        """初始化AI服务管理器单例"""
        try:
            from api_management.core.task_based_ai_service_manager import get_simplified_ai_service_manager
            ai_manager = get_simplified_ai_service_manager()
            if ai_manager is not None:
                print(f"✅ AI服务管理器单例初始化成功: {type(ai_manager).__name__}")
            else:
                print("⚠️ AI服务管理器单例返回None，但不影响Web服务启动")
        except ImportError as e:
            print(f"⚠️ AI服务管理器导入失败: {e}")
            print("   ValidationDrivenExecutor功能将不可用，但Web服务正常启动")
        except Exception as e:
            print(f"⚠️ AI服务管理器初始化异常: {e}")
            print("   ValidationDrivenExecutor功能将不可用，但Web服务正常启动")

# 创建全局应用实例
web_app = WebInterfaceApp()

# Flask应用实例（用于外部访问）
flask_app = web_app.app
socketio = web_app.socketio

if __name__ == '__main__':
    # 直接运行时启动应用
    web_app.run(debug=True)
