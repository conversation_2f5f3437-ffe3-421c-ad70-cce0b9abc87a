/* General Resets & Body Styling */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: #1E1F22;
    color: #BBBBBB;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow: hidden; /* Prevent body scroll */
}

/* Core Nine-Grid Layout */
.nine-grid-container {
    width: 100vw;
    height: 100vh;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    grid-gap: 2px;
    background-color: #1E1F22;
    grid-template-areas:
        "area1-2 area5 area3"
        "area4   area5 area6"
        "area7   area8 area9";
}

/* Grid Area Base Styling */
.grid-area {
    background-color: #2A2D30;
    border: 1px solid #3C3F41;
    padding: 0.1rem 1rem 1rem 1rem;
    overflow-y: auto;
    color: #BBBBBB;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Grid Area Assignments */
.grid-area-1-2 { grid-area: area1-2; }
.grid-area-3 { grid-area: area3; }
.grid-area-4 { grid-area: area4; }
.grid-area-5 { grid-area: area5; }
.grid-area-6 { grid-area: area6; }
.grid-area-7 { grid-area: area7; }
.grid-area-8 { grid-area: area8; }
.grid-area-9 { grid-area: area9; }

/* Common Component Styling */
.area-title {
    font-size: calc(0.5rem + 0.15vw);
    font-weight: bold;
    color: #0078D4;
    margin: 0 0 0.3rem 0;
    border-bottom: 1px solid #3C3F41;
    padding-bottom: 0.2rem;
}

.area-content {
    font-size: calc(0.8rem + 0.2vw);
    line-height: 1.4;
}

/* VSCode Style Scrollbar */
.vscode-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #424242 #1E1F22;
}

.vscode-scrollbar::-webkit-scrollbar {
    width: 14px;
}

.vscode-scrollbar::-webkit-scrollbar-track {
    background: #1E1F22;
}

.vscode-scrollbar::-webkit-scrollbar-thumb {
    background-color: #424242;
    border-radius: 0px;
    border: 3px solid #1E1F22;
}

.vscode-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #4F4F4F;
}

.vscode-scrollbar::-webkit-scrollbar-thumb:active {
    background-color: #6C6C6C;
}

/* 自动隐藏左侧菜单 */
.left-menu-trigger {
    position: fixed;
    left: 0;
    top: 0;
    width: 10px;
    height: 100vh;
    z-index: 999;
    background: transparent;
}

.left-menu {
    position: fixed;
    left: -250px;
    top: 0;
    width: 250px;
    height: 100vh;
    background: rgba(30, 31, 34, 0.95);
    backdrop-filter: blur(10px);
    border-right: 1px solid #3C3F41;
    z-index: 1000;
    transition: transform 0.3s ease;
    padding: 1rem;
    overflow-y: auto;
}

.left-menu.show {
    transform: translateX(250px);
}

.menu-item {
    display: block;
    color: #BBBBBB;
    text-decoration: none;
    padding: 0.8rem 1rem;
    margin: 0.2rem 0;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.menu-item:hover {
    background-color: #393B40;
    color: #0078D4;
}

.menu-title {
    color: #0078D4;
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #3C3F41;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-active { background-color: #4CAF50; }
.status-thinking { background-color: #FF9800; }
.status-converging { background-color: #2196F3; }
.status-completed { background-color: #9C27B0; }
.status-warning { background-color: #F44336; }

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #393B40;
    border-radius: 4px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.progress-fill {
    height: 100%;
    background-color: #0078D4;
    transition: width 0.3s ease;
}

/* V2治理相关样式 */
.governance-progress {
    background: var(--color-surface-secondary, #393B40);
    padding: 0.5rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.governance-risk-hint {
    background: #E65100;
    color: white;
    padding: 0.3rem;
    border-radius: 3px;
    font-size: 0.7rem;
    text-align: center;
    margin-bottom: 0.5rem;
}

.governance-logs {
    background: #2D2D30;
    padding: 0.5rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    font-family: monospace;
    font-size: 0.75rem;
}

@keyframes processingPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* V2知识库可视化样式 */
.knowledge-graph {
    width: 100%;
    height: 200px;
    background: #2D2D30;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.constraint-node {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.6rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid;
}

.constraint-node.global {
    background: #4CAF50;
    border-color: #4CAF50;
    color: white;
}

.constraint-node.local {
    background: #2196F3;
    border-color: #2196F3;
    color: white;
}

.constraint-node.boundary {
    background: #FF9800;
    border-color: #FF9800;
    color: white;
}

.constraint-node.state-machine {
    background: #9C27B0;
    border-color: #9C27B0;
    color: white;
}

.constraint-node:hover {
    transform: scale(1.1);
    z-index: 10;
}

.constraint-connection {
    position: absolute;
    height: 2px;
    background: #666;
    transform-origin: left center;
}

.constraint-connection.fork {
    background: #FF9800;
    border-style: dashed;
}

/* V2风险评分仪表盘 */
.risk-dashboard {
    background: #2D2D30;
    padding: 0.8rem;
    border-radius: 4px;
    margin-bottom: 0.8rem;
}

.risk-score {
    text-align: center;
    margin-bottom: 0.5rem;
}

.risk-score-value {
    font-size: 1.5rem;
    font-weight: bold;
}

.risk-score.critical .risk-score-value {
    color: #F44336;
}

.risk-score.high .risk-score-value {
    color: #FF9800;
}

.risk-score.medium .risk-score-value {
    color: #FFC107;
}

.risk-score.low .risk-score-value {
    color: #4CAF50;
}

.risk-score-label {
    font-size: 0.7rem;
    color: #BBBBBB;
}

/* V2四阶段流程进度 */
.stage-progress {
    margin-bottom: 0.8rem;
}

.stage-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.3rem;
    padding: 0.3rem;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.stage-item.current {
    background: #0078D4;
    color: white;
}

.stage-item.completed {
    background: #4CAF50;
    color: white;
}

.stage-item.pending {
    background: #666;
    color: #999;
}

.stage-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.stage-indicator.current {
    background: #0078D4;
    animation: pulse 2s infinite;
}

.stage-indicator.completed {
    background: #4CAF50;
}

.stage-indicator.pending {
    background: #666;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* V2决策中心卡片样式 */
.decision-card {
    background: #393B40;
    padding: 0.8rem;
    border-radius: 4px;
    margin-bottom: 0.8rem;
    border-left: 4px solid;
}

.decision-card.insight {
    border-left-color: #4CAF50;
}

.decision-card.action {
    border-left-color: #FF9800;
}

.decision-card.log {
    border-left-color: #2196F3;
}

.decision-card-title {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.decision-options {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.decision-btn {
    padding: 0.4rem 0.8rem;
    background: transparent;
    border: 1px solid #0078D4;
    color: #0078D4;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.decision-btn:hover {
    background: #0078D4;
    color: white;
}

.decision-btn.approve {
    border-color: #4CAF50;
    color: #4CAF50;
}

.decision-btn.approve:hover {
    background: #4CAF50;
    color: white;
}

.decision-btn.reject {
    border-color: #F44336;
    color: #F44336;
}

.decision-btn.reject:hover {
    background: #F44336;
    color: white;
}

/* V2输入区域样式 */
.input-section {
    margin-bottom: 0.8rem;
}

.input-field {
    width: 100%;
    background: #3C3F41;
    color: #BBBBBB;
    border: 1px solid #555;
    padding: 0.5rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.input-field:focus {
    outline: none;
    border-color: #0078D4;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    flex: 1;
    padding: 0.5rem;
    background: transparent;
    border: 1px solid;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.action-btn.primary {
    border-color: #0078D4;
    color: #0078D4;
}

.action-btn.primary:hover {
    background: #0078D4;
    color: white;
}

.action-btn.secondary {
    border-color: #4CAF50;
    color: #4CAF50;
}

.action-btn.secondary:hover {
    background: #4CAF50;
    color: white;
}

.action-btn.tertiary {
    border-color: #666;
    color: #666;
}

.action-btn.tertiary:hover {
    background: #666;
    color: white;
}

.action-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

/* V2项目输出样式 */
.output-status {
    text-align: center;
    margin-bottom: 0.8rem;
}

.output-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
}

.output-badge.success {
    background: #4CAF50;
    color: white;
}

.output-badge.processing {
    background: #FF9800;
    color: white;
    animation: processingPulse 1s infinite;
}

.output-badge.error {
    background: #F44336;
    color: white;
}

.output-links {
    margin-top: 0.8rem;
}

.output-link {
    display: block;
    padding: 0.5rem;
    background: #393B40;
    color: #0078D4;
    text-decoration: none;
    border-radius: 3px;
    margin-bottom: 0.3rem;
    transition: background-color 0.2s ease;
}

.output-link:hover {
    background: #0078D4;
    color: white;
}

/* V2约束审查详情样式 */
.constraint-detail {
    background: #2D2D30;
    padding: 0.8rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.constraint-detail-header {
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #0078D4;
}

.constraint-detail-item {
    margin-bottom: 0.3rem;
    font-size: 0.8rem;
}

.constraint-detail-item strong {
    color: #BBBBBB;
}

.constraint-params {
    background: #1E1F22;
    padding: 0.5rem;
    border-radius: 3px;
    font-family: monospace;
    font-size: 0.7rem;
    margin-top: 0.3rem;
}

/* 8号区域详细区样式 */
#detail-area {
    position: relative;
}

#detail-title {
    position: absolute;
    top: 4px;
    left: 8px;
    background: #3C3F41;
    color: #BBBBBB;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7rem;
    z-index: 10;
}

#detail-content {
    padding: 0.5rem;
    font-family: monospace;
    font-size: 0.8rem;
    color: #BBBBBB;
    line-height: 1.3;
    height: 100%;
    display: flex;
    flex-direction: column;
}

#detail-placeholder {
    color: #666;
    text-align: center;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 项目选择器样式 */
.project-selector {
    margin-bottom: 0.5rem;
    padding: 0.3rem;
    background: #2A2D30;
    border: 1px solid #3C3F41;
    border-radius: 4px;
}

.project-selector select {
    flex: 1;
    background: #2A2D30;
    color: #BBBBBB;
    border: 1px solid #3C3F41;
    border-radius: 3px;
    padding: 0.2rem;
    font-size: 0.8rem;
}

.project-selector button {
    padding: 0.2rem 0.4rem;
    background: transparent;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.7rem;
    transition: opacity 0.2s;
}

.project-selector button:hover {
    opacity: 1;
}

/* 控制按钮样式 */
.control-buttons {
    display: flex;
    gap: 0.3rem;
}

.control-buttons button {
    flex: 1;
    padding: 0.3rem;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
    font-size: 0.8rem;
}

.control-buttons button:hover {
    opacity: 1;
}

.control-buttons button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}
