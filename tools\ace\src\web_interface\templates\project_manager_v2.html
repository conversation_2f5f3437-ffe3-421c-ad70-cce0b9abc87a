<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V2项目经理工作台 - 虚拟项目经理交互版</title>
    
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>

    <!-- 复用nine_grid的基础样式和脚本 -->
    <link rel="stylesheet" href="/static/css/nine_grid_base.css">
    <script src="/static/js/nine_grid_base.js"></script>
    
    <style>
        /* V2特有样式 - 复用nine_grid的基础样式 */
        
        /* V2特有样式 - 复用nine_grid的基础样式 */
        
        /* V2知识库可视化样式 */
        .knowledge-graph {
            width: 100%;
            height: 200px;
            background: #2D2D30;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }

        .constraint-node {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.6rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid;
        }

        .constraint-node.global {
            background: #4CAF50;
            border-color: #4CAF50;
            color: white;
        }

        .constraint-node.local {
            background: #2196F3;
            border-color: #2196F3;
            color: white;
        }

        .constraint-node.boundary {
            background: #FF9800;
            border-color: #FF9800;
            color: white;
        }

        .constraint-node.state-machine {
            background: #9C27B0;
            border-color: #9C27B0;
            color: white;
        }

        .constraint-node:hover {
            transform: scale(1.1);
            z-index: 10;
        }

        .constraint-connection {
            position: absolute;
            height: 2px;
            background: #666;
            transform-origin: left center;
        }

        .constraint-connection.fork {
            background: #FF9800;
            height: 3px;
        }

        /* V2风险评估仪表盘 */
        .risk-dashboard {
            background: #2D2D30;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 0.8rem;
        }

        .risk-score {
            text-align: center;
            margin-bottom: 0.5rem;
        }

        .risk-score-value {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .risk-score.critical .risk-score-value {
            color: #F44336;
        }

        .risk-score.high .risk-score-value {
            color: #FF9800;
        }

        .risk-score.medium .risk-score-value {
            color: #FFC107;
        }

        .risk-score.low .risk-score-value {
            color: #4CAF50;
        }

        .risk-score-label {
            font-size: 0.8rem;
            color: #BBBBBB;
            margin-top: 0.2rem;
        }

        /* V2四阶段流程进度 */
        .stage-progress {
            margin-bottom: 1rem;
        }

        .stage-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            padding: 0.3rem;
            border-radius: 3px;
            transition: background-color 0.2s ease;
        }

        .stage-item.current {
            background: rgba(0, 120, 212, 0.1);
            border-left: 3px solid #0078D4;
        }

        .stage-item.completed {
            background: rgba(76, 175, 80, 0.1);
            border-left: 3px solid #4CAF50;
        }

        .stage-item.pending {
            background: rgba(158, 158, 158, 0.1);
            border-left: 3px solid #9E9E9E;
        }

        .stage-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
            flex-shrink: 0;
        }

        .stage-indicator.current {
            background: #0078D4;
            animation: pulse 2s infinite;
        }

        .stage-indicator.completed {
            background: #4CAF50;
        }

        .stage-indicator.pending {
            background: #9E9E9E;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* V2决策中心卡片 */
        .decision-card {
            background: #2D2D30;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
            border-left: 4px solid;
        }

        .decision-card.insight {
            border-left-color: #2196F3;
        }

        .decision-card.action {
            border-left-color: #FF9800;
        }

        .decision-card.log {
            border-left-color: #9E9E9E;
        }

        .decision-card-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #0078D4;
        }

        .decision-options {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .decision-btn {
            padding: 0.4rem 0.8rem;
            background: transparent;
            border: 1px solid #0078D4;
            color: #0078D4;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .decision-btn:hover {
            background: #0078D4;
            color: white;
        }

        .decision-btn.approve {
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .decision-btn.approve:hover {
            background: #4CAF50;
            color: white;
        }

        .decision-btn.reject {
            border-color: #F44336;
            color: #F44336;
        }

        .decision-btn.reject:hover {
            background: #F44336;
            color: white;
        }

        /* V2项目输出样式 */
        .output-status {
            text-align: center;
            margin-bottom: 0.8rem;
        }

        .output-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .output-badge.success {
            background: #4CAF50;
            color: white;
        }

        .output-badge.processing {
            background: #FF9800;
            color: white;
            animation: processingPulse 1s infinite;
        }

        .output-badge.error {
            background: #F44336;
            color: white;
        }

        .output-links {
            margin-top: 0.8rem;
        }

        .output-link {
            display: block;
            padding: 0.5rem;
            background: #393B40;
            color: #0078D4;
            text-decoration: none;
            border-radius: 3px;
            margin-bottom: 0.3rem;
            transition: background-color 0.2s ease;
        }

        .output-link:hover {
            background: #0078D4;
            color: white;
        }

        @keyframes processingPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* V2约束详情样式 */
        .constraint-detail {
            background: #2D2D30;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
        }

        .constraint-detail-header {
            font-weight: bold;
            color: #0078D4;
            margin-bottom: 0.5rem;
            border-bottom: 1px solid #3C3F41;
            padding-bottom: 0.3rem;
        }

        .constraint-detail-item {
            margin-bottom: 0.3rem;
            font-size: 0.9rem;
        }

        .constraint-detail-item strong {
            color: #BBBBBB;
        }

        .constraint-params {
            background: #1E1F22;
            padding: 0.5rem;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.7rem;
            margin-top: 0.3rem;
        }



        /* 项目选择器样式 */
        .project-selector {
            margin-bottom: 0.5rem;
            padding: 0.3rem;
            background: #2A2D30;
            border: 1px solid #3C3F41;
            border-radius: 4px;
        }

        .project-selector select {
            flex: 1;
            background: #2A2D30;
            color: #BBBBBB;
            border: 1px solid #3C3F41;
            border-radius: 3px;
            padding: 0.2rem;
            font-size: 0.8rem;
        }

        .project-selector button {
            padding: 0.2rem 0.4rem;
            background: transparent;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.7rem;
            transition: opacity 0.2s;
        }

        .project-selector button:hover {
            opacity: 1;
        }





        /* V2知识库可视化样式 */
        .knowledge-graph {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #2D2D30, #1E1F22);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
            border: 1px solid #3C3F41;
        }

        .constraint-node {
            position: absolute;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.6rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .constraint-node:hover {
            transform: scale(1.2);
            z-index: 10;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
        }

        .constraint-node.global {
            background: linear-gradient(135deg, #4CAF50, #66BB6A);
            border-color: #4CAF50;
            color: white;
        }

        .constraint-node.global:hover {
            box-shadow: 0 4px 16px rgba(76, 175, 80, 0.6);
        }

        .constraint-node.local {
            background: linear-gradient(135deg, #2196F3, #42A5F5);
            border-color: #2196F3;
            color: white;
        }

        .constraint-node.local:hover {
            box-shadow: 0 4px 16px rgba(33, 150, 243, 0.6);
        }

        .constraint-node.boundary {
            background: linear-gradient(135deg, #FF9800, #FFA726);
            border-color: #FF9800;
            color: white;
        }

        .constraint-node.boundary:hover {
            box-shadow: 0 4px 16px rgba(255, 152, 0, 0.6);
        }

        .constraint-node.state-machine {
            background: linear-gradient(135deg, #9C27B0, #AB47BC);
            border-color: #9C27B0;
            color: white;
        }

        .constraint-node.state-machine:hover {
            box-shadow: 0 4px 16px rgba(156, 39, 176, 0.6);
        }

        /* 约束连接线 */
        .constraint-connection {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, rgba(187, 187, 187, 0.3), rgba(187, 187, 187, 0.1));
            transform-origin: left center;
            pointer-events: none;
            z-index: 1;
        }

        .constraint-connection.active {
            background: linear-gradient(90deg, rgba(0, 120, 212, 0.6), rgba(0, 120, 212, 0.2));
            height: 3px;
        }

        /* 节点标签 */
        .node-label {
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.5rem;
            color: #888;
            white-space: nowrap;
            pointer-events: none;
        }

        /* 节点详情提示 */
        .node-tooltip {
            position: absolute;
            background: rgba(42, 45, 48, 0.95);
            border: 1px solid #3C3F41;
            border-radius: 4px;
            padding: 0.5rem;
            font-size: 0.7rem;
            color: #BBBBBB;
            z-index: 20;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            max-width: 200px;
        }

        .node-tooltip.show {
            opacity: 1;
        }

        /* V2决策中心卡片样式 */
        .decision-card {
            background: linear-gradient(135deg, #2A2D30, #1E1F22);
            border: 1px solid #3C3F41;
            border-radius: 6px;
            padding: 0.8rem;
            margin-bottom: 0.8rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .decision-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, transparent, rgba(0, 120, 212, 0.6), transparent);
        }

        .decision-card.insight::before {
            background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.6), transparent);
        }

        .decision-card.action::before {
            background: linear-gradient(90deg, transparent, rgba(255, 152, 0, 0.8), transparent);
            animation: pulse-warning 2s infinite;
        }

        .decision-card.log::before {
            background: linear-gradient(90deg, transparent, rgba(33, 150, 243, 0.6), transparent);
        }

        @keyframes pulse-warning {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }

        .decision-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            border-color: #0078D4;
        }

        .decision-card-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .decision-card.insight .decision-card-title {
            color: #4CAF50;
        }

        .decision-card.action .decision-card-title {
            color: #FF9800;
        }

        .decision-card.log .decision-card-title {
            color: #2196F3;
        }

        .decision-options {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.8rem;
        }

        .decision-btn {
            flex: 1;
            padding: 0.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .decision-btn.approve {
            background: linear-gradient(135deg, #4CAF50, #66BB6A);
            color: white;
        }

        .decision-btn.approve:hover {
            background: linear-gradient(135deg, #66BB6A, #4CAF50);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
        }

        .decision-btn.reject {
            background: linear-gradient(135deg, #F44336, #EF5350);
            color: white;
        }

        .decision-btn.reject:hover {
            background: linear-gradient(135deg, #EF5350, #F44336);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(244, 67, 54, 0.4);
        }

        /* 决策日志样式 */
        #decision-log {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.75rem;
            max-height: 150px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
            padding: 0.5rem;
        }

        #decision-log div {
            margin-bottom: 0.2rem;
            padding: 0.1rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        #decision-log div:last-child {
            border-bottom: none;
        }



        /* V2响应式布局和字体层次优化 */
        .area-title {
            font-size: calc(0.5rem + 0.15vw);
            font-weight: bold;
            color: #0078D4;
            margin: 0 0 0.3rem 0;
            border-bottom: 1px solid #3C3F41;
            padding-bottom: 0.2rem;
        }

        .area-content {
            font-size: clamp(0.75rem, 1vw, 0.9rem);
            line-height: 1.5;
            padding: 0.2rem;
        }

        /* 统一间距系统 */
        .status-item {
            margin-bottom: 0.6rem;
            padding: 0.3rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stage-item {
            margin-bottom: 0.5rem;
            padding: 0.4rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
        }

        /* 改进的卡片间距 */
        .decision-card {
            margin-bottom: 1rem;
            padding: 1rem;
        }

        .decision-card:last-child {
            margin-bottom: 0;
        }

        /* 响应式字体大小 */
        @media (max-width: 1200px) {
            .area-title {
                font-size: 0.9rem;
            }

            .area-content {
                font-size: 0.8rem;
            }

            .constraint-node {
                width: 40px;
                height: 40px;
                font-size: 0.5rem;
            }

            .circular-progress {
                width: 60px;
                height: 60px;
            }
        }

        @media (max-width: 900px) {
            .nine-grid-container {
                grid-gap: 1px;
            }

            .grid-area {
                padding: 0.5rem;
            }

            .area-title {
                font-size: 0.8rem;
                margin-bottom: 0.5rem;
            }

            .area-content {
                font-size: 0.75rem;
            }
        }

        /* 改进的滚动条样式 */
        .vscode-scrollbar::-webkit-scrollbar {
            width: 8px;
        }

        .vscode-scrollbar::-webkit-scrollbar-track {
            background: #1E1F22;
            border-radius: 4px;
        }

        .vscode-scrollbar::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #424242, #2A2D30);
            border-radius: 4px;
            border: 1px solid #1E1F22;
        }

        .vscode-scrollbar::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, #4F4F4F, #3C3F41);
        }

        /* 统一的输入框样式 */
        input, textarea, select {
            background: #2A2D30 !important;
            color: #BBBBBB !important;
            border: 1px solid #3C3F41 !important;
            border-radius: 4px !important;
            padding: 0.5rem !important;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            transition: border-color 0.3s ease !important;
        }

        input:focus, textarea:focus, select:focus {
            outline: none !important;
            border-color: #0078D4 !important;
            box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2) !important;
        }

        /* 改进的按钮样式 */
        button {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-weight: 500;
            letter-spacing: 0.3px;
            transition: all 0.3s ease;
        }

        /* 数据展示的字体层次 */
        .metric-value {
            font-weight: 700;
            font-size: 1.1em;
        }

        .metric-label {
            font-weight: 400;
            opacity: 0.9;
        }

        .secondary-text {
            font-size: 0.85em;
            opacity: 0.7;
        }

        /* V4.2阶段零突出显示样式 */
        .stage-zero-highlight {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 4px;
            padding: 0.5rem;
            position: relative;
        }

        .badge {
            display: inline-block;
            padding: 0.1rem 0.4rem;
            border-radius: 3px;
            font-size: 0.6rem;
            font-weight: bold;
            margin-left: 0.5rem;
        }

        .badge-reliability {
            background: linear-gradient(135deg, #4CAF50, #66BB6A);
            color: white;
            box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
        }

        .stage-zero-metrics {
            margin-top: 1rem;
            padding: 0.8rem;
            background: linear-gradient(135deg, rgba(0, 120, 212, 0.05), rgba(0, 120, 212, 0.02));
            border: 1px solid rgba(0, 120, 212, 0.2);
            border-radius: 4px;
        }

        .metrics-title {
            font-weight: bold;
            margin-bottom: 0.8rem;
            color: #0078D4;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .metric-item {
            margin-bottom: 0.6rem;
        }

        .metric-label {
            font-size: 0.85rem;
            color: #BBBBBB;
        }

        .metric-value {
            font-weight: bold;
            font-size: 0.9rem;
        }

        .metric-value.success {
            color: #4CAF50;
        }

        .metric-value.info {
            color: #2196F3;
        }

        .metric-value.warning {
            color: #FF9800;
        }

        /* V4.2 AtomicConstraint详细展示样式 */
        .constraint-detail-enhanced {
            padding: 1rem;
            background: linear-gradient(135deg, #2A2D30, #1E1F22);
            border: 1px solid #3C3F41;
            border-radius: 6px;
            font-family: 'Consolas', 'Monaco', monospace;
        }

        .constraint-header {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #3C3F41;
        }

        .constraint-id {
            font-size: 1.2rem;
            font-weight: bold;
            color: #0078D4;
            background: rgba(0, 120, 212, 0.1);
            padding: 0.3rem 0.6rem;
            border-radius: 4px;
        }

        .constraint-category {
            padding: 0.2rem 0.5rem;
            border-radius: 3px;
            font-size: 0.7rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .category-boundary-condition {
            background: #FF9800;
            color: white;
        }

        .category-constraint {
            background: #4CAF50;
            color: white;
        }

        .category-guardrail {
            background: #F44336;
            color: white;
        }

        .category-state-machine {
            background: #9C27B0;
            color: white;
        }

        .constraint-section {
            margin-bottom: 1rem;
        }

        .section-title {
            font-weight: bold;
            color: #0078D4;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .field-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
        }

        .field-item {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            padding: 0.3rem;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 3px;
        }

        .field-label {
            color: #888;
            font-size: 0.8rem;
            min-width: 60px;
        }

        .field-value {
            color: #BBBBBB;
            font-weight: bold;
            font-size: 0.8rem;
        }

        .params-tree {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
            padding: 0.5rem;
        }

        .param-node {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.2rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .param-node:last-child {
            border-bottom: none;
        }

        .param-key {
            color: #4FC3F7;
            font-size: 0.8rem;
        }

        .param-value {
            color: #81C784;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .lineage-info {
            background: rgba(0, 120, 212, 0.05);
            border-radius: 4px;
            padding: 0.5rem;
        }

        .lineage-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.2rem 0;
        }

        .lineage-label {
            color: #0078D4;
            font-size: 0.8rem;
        }

        .lineage-value {
            color: #BBBBBB;
            font-size: 0.8rem;
            font-weight: bold;
        }

        /* V4.2 引用式分叉机制可视化样式 */
        .constraint-node.forked {
            border: 2px dashed #FF9800;
            position: relative;
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 152, 0, 0.05));
        }

        .constraint-node.forked:hover {
            border-color: #FFB74D;
            box-shadow: 0 4px 16px rgba(255, 152, 0, 0.6);
        }

        .fork-indicator {
            position: absolute;
            bottom: -18px;
            right: -8px;
            font-size: 0.5rem;
            color: #FF9800;
            background: rgba(255, 152, 0, 0.1);
            padding: 0.1rem 0.2rem;
            border-radius: 2px;
            border: 1px solid #FF9800;
            font-weight: bold;
        }

        .constraint-connection.reference {
            background: linear-gradient(90deg, rgba(33, 150, 243, 0.6), rgba(33, 150, 243, 0.2));
            height: 3px;
            border-radius: 2px;
        }

        .constraint-connection.fork {
            background: linear-gradient(90deg, rgba(255, 152, 0, 0.8), rgba(255, 152, 0, 0.3));
            height: 2px;
            border-style: dashed;
            border-top: 2px dashed #FF9800;
            background: none;
        }

        /* 约束类别特定样式 */
        .constraint-node[data-category="boundary_condition"] {
            border-color: #FF9800;
        }

        .constraint-node[data-category="constraint"] {
            border-color: #4CAF50;
        }

        .constraint-node[data-category="guardrail"] {
            border-color: #F44336;
        }

        .constraint-node[data-category="state_machine"] {
            border-color: #9C27B0;
        }

        /* V4.2 AI-算法协同展示样式 */
        .ai-algorithm-collaboration {
            background: linear-gradient(135deg, rgba(0, 120, 212, 0.1), rgba(0, 120, 212, 0.05));
            border: 1px solid rgba(0, 120, 212, 0.3);
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .collaboration-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid rgba(0, 120, 212, 0.2);
        }

        .ai-role, .algorithm-role {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.3rem 0.6rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .ai-role {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.1));
            color: #4CAF50;
        }

        .algorithm-role {
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.2), rgba(255, 152, 0, 0.1));
            color: #FF9800;
        }

        .collaboration-arrow {
            font-size: 1.2rem;
            color: #0078D4;
            font-weight: bold;
        }

        .collaboration-process {
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
        }

        .ai-step, .algorithm-step, .result-step {
            padding: 0.6rem;
            border-radius: 4px;
            border-left: 3px solid;
        }

        .ai-step {
            background: rgba(76, 175, 80, 0.05);
            border-left-color: #4CAF50;
        }

        .algorithm-step {
            background: rgba(255, 152, 0, 0.05);
            border-left-color: #FF9800;
        }

        .result-step {
            background: rgba(33, 150, 243, 0.05);
            border-left-color: #2196F3;
        }

        .step-header {
            font-weight: bold;
            font-size: 0.8rem;
            margin-bottom: 0.3rem;
            color: #0078D4;
        }

        .step-content {
            font-size: 0.8rem;
            color: #BBBBBB;
        }

        .validation-checks {
            display: flex;
            flex-direction: column;
            gap: 0.2rem;
        }

        .check-item {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            font-size: 0.75rem;
        }

        .check-item.success {
            color: #4CAF50;
        }

        .check-icon {
            font-weight: bold;
        }

        .result-badge {
            display: inline-block;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-size: 0.7rem;
            font-weight: bold;
            margin-right: 0.5rem;
        }

        .result-badge.success {
            background: #4CAF50;
            color: white;
        }

        .result-detail {
            font-size: 0.8rem;
            color: #BBBBBB;
            margin-top: 0.2rem;
        }

        /* 紧凑版AI-算法协同样式 (用于区域5) */
        .ai-algorithm-collaboration-compact {
            background: linear-gradient(135deg, rgba(0, 120, 212, 0.08), rgba(0, 120, 212, 0.03));
            border: 1px solid rgba(0, 120, 212, 0.2);
            border-radius: 4px;
            padding: 0.6rem;
            margin-bottom: 0.8rem;
        }

        .collaboration-header-compact {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .ai-role-compact, .algorithm-role-compact {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            font-size: 0.7rem;
            font-weight: bold;
        }

        .ai-role-compact {
            color: #4CAF50;
        }

        .algorithm-role-compact {
            color: #FF9800;
        }

        .validation-status-compact {
            font-size: 0.7rem;
        }

        /* V4.2 风险预防措施样式 */
        .risk-prevention-panel {
            margin-bottom: 1rem;
            padding: 0.8rem;
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.05), rgba(76, 175, 80, 0.02));
            border: 1px solid rgba(76, 175, 80, 0.2);
            border-radius: 4px;
        }

        .panel-title {
            font-weight: bold;
            margin-bottom: 0.8rem;
            color: #4CAF50;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .prevention-item {
            margin-bottom: 0.8rem;
            padding: 0.6rem;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 4px;
            border-left: 3px solid #4CAF50;
        }

        .prevention-item:last-child {
            margin-bottom: 0;
        }

        .risk-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.4rem;
        }

        .prevention-status {
            padding: 0.1rem 0.4rem;
            border-radius: 3px;
            font-size: 0.6rem;
            font-weight: bold;
        }

        .prevention-status.prevented {
            background: #4CAF50;
            color: white;
        }

        .prevention-status.monitoring {
            background: #FF9800;
            color: white;
        }

        .risk-description {
            font-size: 0.8rem;
            color: #BBBBBB;
            margin-bottom: 0.4rem;
            line-height: 1.3;
        }

        .prevention-detail {
            font-size: 0.75rem;
            color: #888;
            line-height: 1.3;
        }

        .prevention-label {
            color: #4CAF50;
            font-weight: bold;
        }

        /* 文档健康报告样式 */
        .health-report-section {
            padding: 0.8rem;
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.05), rgba(33, 150, 243, 0.02));
            border: 1px solid rgba(33, 150, 243, 0.2);
            border-radius: 4px;
        }

        .report-title {
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 0.6rem;
            font-size: 0.9rem;
        }

        .report-summary {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .summary-label {
            font-size: 0.8rem;
            color: #BBBBBB;
        }

        .summary-value {
            font-weight: bold;
            font-size: 0.8rem;
        }

        .summary-value.success {
            color: #4CAF50;
        }

        .summary-value.info {
            color: #2196F3;
        }

        .summary-value.warning {
            color: #FF9800;
        }

        /* 知识库帮助图标样式 */
        .help-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 18px;
            height: 18px;
            margin-left: 0.5rem;
            cursor: pointer;
            border-radius: 50%;
            background: linear-gradient(135deg, #0078D4, #40A9FF);
            transition: all 0.3s ease;
            position: relative;
        }

        .help-icon:hover {
            background: linear-gradient(135deg, #40A9FF, #0078D4);
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0, 120, 212, 0.4);
        }

        .help-symbol {
            color: white;
            font-size: 0.7rem;
            font-weight: bold;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* 知识库帮助弹窗样式 */
        .knowledge-help-popup {
            position: absolute;
            background: linear-gradient(135deg, #2A2D30, #1E1F22);
            border: 2px solid #0078D4;
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5);
            z-index: 1000;
            min-width: 300px;
            max-width: 400px;
            font-size: 0.8rem;
            line-height: 1.4;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .knowledge-help-popup.show {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        .help-popup-title {
            font-weight: bold;
            color: #0078D4;
            margin-bottom: 0.8rem;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .help-popup-section {
            margin-bottom: 0.8rem;
        }

        .help-popup-section:last-child {
            margin-bottom: 0;
        }

        .help-section-title {
            font-weight: bold;
            color: #BBBBBB;
            margin-bottom: 0.4rem;
            font-size: 0.85rem;
        }

        .help-legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.3rem;
            gap: 0.5rem;
        }

        .help-legend-item:last-child {
            margin-bottom: 0;
        }

        .help-legend-symbol {
            min-width: 20px;
            text-align: center;
            font-weight: bold;
        }

        .help-legend-text {
            color: #BBBBBB;
            font-size: 0.8rem;
        }

        .help-close-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            font-size: 1rem;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .help-close-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #BBBBBB;
        }

        /* 区域5算法思维日志>和v符号指示器和折叠功能 */
        .grid-area-5 .log-entry {
            position: relative;
        }

        .grid-area-5 .log-arrows {
            margin-left: 6px;
            font-size: 0.6rem; /* 极小字体 */
        }

        .grid-area-5 .arrow-ai-comm,
        .grid-area-5 .arrow-py-ops {
            cursor: pointer;
            font-family: monospace; /* 确保符号显示一致 */
            transition: all 0.2s ease; /* 平滑状态切换 */
        }

        .grid-area-5 .arrow-ai-comm {
            color: #4FC3F7;
            margin-right: 4px; /* 缩小间距 */
        }

        .grid-area-5 .arrow-py-ops {
            color: #81C784;
        }

        /* 收起状态：显示 > */
        .grid-area-5 .arrow-ai-comm:not(.expanded)::before {
            content: ">";
        }

        .grid-area-5 .arrow-py-ops:not(.expanded)::before {
            content: ">";
        }

        /* 展开状态：显示 v */
        .grid-area-5 .arrow-ai-comm.expanded::before {
            content: "v";
        }

        .grid-area-5 .arrow-py-ops.expanded::before {
            content: "v";
        }

        .grid-area-5 .arrow-ai-comm:hover,
        .grid-area-5 .arrow-py-ops:hover {
            opacity: 0.7;
        }

        .grid-area-5 .expanded-details {
            margin-top: 8px;
            padding: 8px;
            background: #1E1F22;
            border-left: 3px solid #0078D4;
            border-radius: 3px;
            font-size: 0.7rem;
            line-height: 1.3;
        }

        .grid-area-5 .ai-comm-details {
            border-left-color: #4FC3F7;
        }

        .grid-area-5 .py-ops-details {
            border-left-color: #81C784;
        }

        .constraint-node.state-machine {
            background: #9C27B0;
            border-color: #9C27B0;
            color: white;
        }

        .constraint-node:hover {
            transform: scale(1.1);
            z-index: 10;
        }

        /* V2风险评分仪表盘 */
        .risk-dashboard {
            background: #2D2D30;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 0.8rem;
        }

        .risk-score {
            text-align: center;
            margin-bottom: 0.5rem;
        }

        .risk-score-value {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .risk-score.critical .risk-score-value {
            color: #F44336;
        }

        .risk-score.high .risk-score-value {
            color: #FF9800;
        }

        .risk-score.medium .risk-score-value {
            color: #FFC107;
        }

        .risk-score.low .risk-score-value {
            color: #4CAF50;
        }

        .risk-score-label {
            font-size: 0.7rem;
            color: #BBBBBB;
        }

        /* V2四阶段流程进度 */
        .stage-progress {
            margin-bottom: 0.8rem;
        }

        .stage-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.3rem;
            padding: 0.3rem;
            border-radius: 3px;
            transition: background-color 0.2s ease;
        }

        .stage-item.current {
            background: #0078D4;
            color: white;
        }

        .stage-item.completed {
            background: #4CAF50;
            color: white;
        }

        .stage-item.pending {
            background: #666;
            color: #999;
        }

        .stage-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .stage-indicator.current {
            background: #0078D4;
            animation: pulse 2s infinite;
        }

        .stage-indicator.completed {
            background: #4CAF50;
        }

        .stage-indicator.pending {
            background: #666;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* V2决策中心卡片样式 */
        .decision-card {
            background: #393B40;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 0.8rem;
            border-left: 4px solid;
        }

        .decision-card.insight {
            border-left-color: #4CAF50;
        }

        .decision-card.action {
            border-left-color: #FF9800;
        }

        .decision-card.log {
            border-left-color: #2196F3;
        }

        .decision-card-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .decision-options {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .decision-btn {
            padding: 0.4rem 0.8rem;
            background: transparent;
            border: 1px solid #0078D4;
            color: #0078D4;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .decision-btn:hover {
            background: #0078D4;
            color: white;
        }

        .decision-btn.approve {
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .decision-btn.approve:hover {
            background: #4CAF50;
            color: white;
        }

        .decision-btn.reject {
            border-color: #F44336;
            color: #F44336;
        }

        .decision-btn.reject:hover {
            background: #F44336;
            color: white;
        }

        /* V2输入区域样式 */
        .input-section {
            margin-bottom: 0.8rem;
        }

        .input-field {
            width: 100%;
            background: #3C3F41;
            color: #BBBBBB;
            border: 1px solid #555;
            padding: 0.5rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .input-field:focus {
            outline: none;
            border-color: #0078D4;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            flex: 1;
            padding: 0.5rem;
            background: transparent;
            border: 1px solid;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .action-btn.primary {
            border-color: #0078D4;
            color: #0078D4;
        }

        .action-btn.primary:hover {
            background: #0078D4;
            color: white;
        }

        .action-btn.secondary {
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .action-btn.secondary:hover {
            background: #4CAF50;
            color: white;
        }

        .action-btn.tertiary {
            border-color: #666;
            color: #666;
        }

        .action-btn.tertiary:hover {
            background: #666;
            color: white;
        }

        .action-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
        }

        /* V2项目输出样式 */
        .output-status {
            text-align: center;
            margin-bottom: 0.8rem;
        }

        .output-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .output-badge.success {
            background: #4CAF50;
            color: white;
        }

        .output-badge.processing {
            background: #FF9800;
            color: white;
            animation: processingPulse 1s infinite;
        }

        .output-badge.error {
            background: #F44336;
            color: white;
        }

        .output-links {
            margin-top: 0.8rem;
        }

        .output-link {
            display: block;
            padding: 0.5rem;
            background: #393B40;
            color: #0078D4;
            text-decoration: none;
            border-radius: 3px;
            margin-bottom: 0.3rem;
            transition: background-color 0.2s ease;
        }

        .output-link:hover {
            background: #0078D4;
            color: white;
        }

        @keyframes processingPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <!-- 左侧菜单感应区域 -->
    <div class="left-menu-trigger" id="leftMenuTrigger"></div>
    
    <!-- 自动隐藏左侧菜单 -->
    <nav class="left-menu" id="leftMenu">
        <div class="menu-title">🏗️ V2项目经理工作台</div>
        <a href="/" class="menu-item" data-target="home">🏠 主页</a>
        <a href="/nine-grid" class="menu-item" data-target="nine-grid">🔧 九宫格系统</a>
        <a href="/debug" class="menu-item" data-target="debug">🐛 调试中心</a>
        <a href="/api/status" class="menu-item" data-target="status" target="_blank">📊 系统状态</a>
        <a href="/api/health" class="menu-item" data-target="health" target="_blank">❤️ 健康检查</a>
        <div class="menu-item" onclick="openConfigCenter()">⚙️ 配置中心</div>
    </nav>

    <!-- 九宫格主容器 -->
    <div class="nine-grid-container">
        <!-- 区域1-2：项目进度监控 (Process Overview) -->
        <div class="grid-area grid-area-1-2 vscode-scrollbar">
            <div class="area-title">项目进度监控 (Process Overview)</div>
            <div class="area-content">
                <!-- V4.2四阶段流程进度 -->
                <div class="stage-progress">
                    <div class="stage-item stage-zero-highlight current">
                        <span class="status-indicator status-thinking"></span>
                        <span>阶段零：标准化与预验证</span>
                        <span class="badge badge-reliability">可靠性基石</span>
                    </div>
                    <div class="stage-item pending">
                        <span class="status-indicator status-pending"></span>
                        <span>阶段一：全局契约生成</span>
                    </div>
                    <div class="stage-item pending">
                        <span class="status-indicator status-pending"></span>
                        <span>阶段二：引用式契约生成</span>
                    </div>
                    <div class="stage-item pending">
                        <span class="status-indicator status-pending"></span>
                        <span>阶段三：契约履行与审计</span>
                    </div>
                    <div class="stage-item pending">
                        <span class="status-indicator status-pending"></span>
                        <span>阶段四：整体性审计</span>
                    </div>
                </div>

                <!-- 阶段零专用指标 -->
                <div class="stage-zero-metrics">
                    <div class="metrics-title">
                        <span>🛡️</span>
                        <span>阶段零预验证指标</span>
                    </div>

                    <div class="metric-item">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span class="metric-label">预验证通过率</span>
                            <span class="metric-value success">100%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: 100%;"></div>
                        </div>
                    </div>

                    <div class="metric-item">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span class="metric-label">冲突预防数量</span>
                            <span class="metric-value info">3</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill info" style="width: 75%;"></div>
                        </div>
                    </div>

                    <div class="metric-item">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span class="metric-label">Schema验证通过</span>
                            <span class="metric-value success">25/25</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: 100%;"></div>
                        </div>
                    </div>
                </div>

                <!-- 关键指标统计 -->
                <div style="margin-top: 1rem;">
                    <div style="font-weight: bold; margin-bottom: 0.5rem;">关键指标:</div>

                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>已发现原子约束</span>
                            <span style="color: #4CAF50; font-weight: bold;" id="atomic-constraints-count">25</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: 62.5%;"></div>
                        </div>
                    </div>

                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>已生成全局契约点</span>
                            <span style="color: #2196F3; font-weight: bold;" id="global-contracts-count">18</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill info" style="width: 72%;"></div>
                        </div>
                    </div>

                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>已处理文档</span>
                            <span style="color: #FF9800; font-weight: bold;"><span id="processed-docs-count">3</span>/5</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill warning" style="width: 60%;"></div>
                        </div>
                    </div>

                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>当前可靠性评分</span>
                            <span style="color: #4CAF50; font-weight: bold;" id="current-reliability-score">87.7%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: 87.7%;"></div>
                        </div>
                    </div>
                </div>

                <!-- 整体进度 -->
                <div style="margin-top: 1rem;">
                    <div style="margin-bottom: 0.5rem; font-weight: bold;">整体进度:</div>
                    <div style="background: #2A2D30; padding: 0.8rem; border-radius: 4px; border: 1px solid #3C3F41;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.3rem;">
                            <span>阶段零进行中</span>
                            <span style="color: #0078D4; font-weight: bold;">25%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill primary" id="overall-progress" style="width: 25%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 区域5：Python主持人算法思维（垂直打通） -->
        <div class="grid-area grid-area-5 vscode-scrollbar">
            <div class="area-title">Python主持人算法思维</div>
            <div class="area-content" style="height: calc(100% - 2rem); overflow-y: auto;">
                <!-- V4.2 AI-算法协同展示 -->
                <div class="ai-algorithm-collaboration-compact">
                    <div class="collaboration-header-compact">
                        <div class="ai-role-compact">
                            <span class="role-icon">🤖</span>
                            <span>首席架构师AI</span>
                        </div>
                        <div class="collaboration-arrow">→</div>
                        <div class="algorithm-role-compact">
                            <span class="role-icon">⚙️</span>
                            <span>ConstraintPreprocessor</span>
                        </div>
                    </div>

                    <div class="validation-status-compact">
                        <div class="check-item success">
                            <span class="check-icon">✓</span>
                            <span>预验证通过 - 已生成GB001</span>
                        </div>
                    </div>
                </div>

                <!-- V4扫描日志预留位置 -->
                <div class="scanning-logs" style="display:none;">扫描日志将在此显示</div>

                <!-- Python主持人算法思维过程 -->
                <div id="process-log" style="font-family: monospace; font-size: 0.8rem; margin-bottom: 1rem;">
                    <div class="log-entry expandable" onclick="showLogDetail(this, 'startup_check_ide')" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#393B40'" onmouseout="this.style.backgroundColor='transparent'">
                        [14:17:30] 启动检查: 正在验证IDE AI连接状态...✅ 连接正常
                        <span class="log-arrows">
                            <span class="arrow-ai-comm" onclick="event.stopPropagation(); toggleLogDetail(this, 'ai-comm', 'startup_check_ide')"></span>
                            <span class="arrow-py-ops" onclick="event.stopPropagation(); toggleLogDetail(this, 'py-ops', 'startup_check_ide')"></span>
                        </span>
                    </div>
                    <div class="log-entry expandable" onclick="showLogDetail(this, 'startup_check_meeting')" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#393B40'" onmouseout="this.style.backgroundColor='transparent'">
                        [14:17:31] 启动检查: 正在验证Meeting目录权限...✅ 读写权限正常
                        <span class="log-arrows">
                            <span class="arrow-ai-comm" onclick="event.stopPropagation(); toggleLogDetail(this, 'ai-comm', 'startup_check_meeting')"></span>
                            <span class="arrow-py-ops" onclick="event.stopPropagation(); toggleLogDetail(this, 'py-ops', 'startup_check_meeting')"></span>
                        </span>
                    </div>
                    <div class="log-entry expandable" onclick="showLogDetail(this, 'startup_check_algorithms')" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#393B40'" onmouseout="this.style.backgroundColor='transparent'">
                        [14:17:32] 启动检查: 正在加载12种逻辑分析方法...✅ 算法库加载完成
                        <span class="log-arrows">
                            <span class="arrow-ai-comm" onclick="event.stopPropagation(); toggleLogDetail(this, 'ai-comm', 'startup_check_algorithms')"></span>
                            <span class="arrow-py-ops" onclick="event.stopPropagation(); toggleLogDetail(this, 'py-ops', 'startup_check_algorithms')"></span>
                        </span>
                    </div>
                    <div class="log-entry expandable" onclick="showLogDetail(this, 'task_analysis')" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#393B40'" onmouseout="this.style.backgroundColor='transparent'">
                        [14:17:33] 当前任务: 开始执行完备度检查阶段，分析设计文档完整性
                        <span class="log-arrows">
                            <span class="arrow-ai-comm" onclick="event.stopPropagation(); toggleLogDetail(this, 'ai-comm', 'task_analysis')"></span>
                            <span class="arrow-py-ops" onclick="event.stopPropagation(); toggleLogDetail(this, 'py-ops', 'task_analysis')"></span>
                        </span>
                    </div>
                    <div class="log-entry expandable" onclick="showLogDetail(this, 'document_scan')" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#393B40'" onmouseout="this.style.backgroundColor='transparent'">
                        [14:17:34] 正在分析: 扫描docs/目录结构，发现47个文档文件
                        <span class="log-arrows">
                            <span class="arrow-ai-comm" onclick="event.stopPropagation(); toggleLogDetail(this, 'ai-comm', 'document_scan')"></span>
                            <span class="arrow-py-ops" onclick="event.stopPropagation(); toggleLogDetail(this, 'py-ops', 'document_scan')"></span>
                        </span>
                    </div>
                    <div class="log-entry expandable" onclick="showLogDetail(this, 'thinking_judgment')" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#393B40'" onmouseout="this.style.backgroundColor='transparent'">
                        [14:17:35] 思维判断: 设计文档缺少3个关键架构决策点，这会影响后续推理
                        <span class="log-arrows">
                            <span class="arrow-ai-comm" onclick="event.stopPropagation(); toggleLogDetail(this, 'ai-comm', 'thinking_judgment')"></span>
                            <span class="arrow-py-ops" onclick="event.stopPropagation(); toggleLogDetail(this, 'py-ops', 'thinking_judgment')"></span>
                        </span>
                    </div>
                    <div class="log-entry expandable" onclick="showLogDetail(this, 'confidence_calculation')" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#393B40'" onmouseout="this.style.backgroundColor='transparent'">
                        [14:17:36] 置信度计算: 基于现有47个文档，完整度87.7%，接近95%阈值
                        <span class="log-arrows">
                            <span class="arrow-ai-comm" onclick="event.stopPropagation(); toggleLogDetail(this, 'ai-comm', 'confidence_calculation')"></span>
                            <span class="arrow-py-ops" onclick="event.stopPropagation(); toggleLogDetail(this, 'py-ops', 'confidence_calculation')"></span>
                        </span>
                    </div>
                    <div class="log-entry expandable" onclick="showLogDetail(this, 'question_generation')" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#393B40'" onmouseout="this.style.backgroundColor='transparent'">
                        [14:17:37] 准备执行: 生成智能选择题，请求人类补全架构决策逻辑
                        <span class="log-arrows">
                            <span class="arrow-ai-comm" onclick="event.stopPropagation(); toggleLogDetail(this, 'ai-comm', 'question_generation')"></span>
                            <span class="arrow-py-ops" onclick="event.stopPropagation(); toggleLogDetail(this, 'py-ops', 'question_generation')"></span>
                        </span>
                    </div>
                    <div class="log-entry expandable" onclick="showLogDetail(this, 'waiting_human')" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#393B40'" onmouseout="this.style.backgroundColor='transparent'">
                        [14:17:38] 等待中: 暂停自动推理，等待人类智慧输入以继续下一阶段
                        <span class="log-arrows">
                            <span class="arrow-ai-comm" onclick="event.stopPropagation(); toggleLogDetail(this, 'ai-comm', 'waiting_human')"></span>
                            <span class="arrow-py-ops" onclick="event.stopPropagation(); toggleLogDetail(this, 'py-ops', 'waiting_human')"></span>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 区域3：项目风险评估 (Risk Assessment) -->
        <div class="grid-area grid-area-3 vscode-scrollbar">
            <div class="area-title">项目风险评估 (Risk Assessment)</div>
            <div class="area-content">
                <!-- 可靠性评分仪表盘 -->
                <div class="circular-progress">
                    <svg>
                        <circle class="progress-circle progress-bg" cx="40" cy="40" r="36"></circle>
                        <circle class="progress-circle progress-bar-circle danger" cx="40" cy="40" r="36"
                                style="stroke-dashoffset: 79.1;" id="reliability-circle"></circle>
                    </svg>
                    <div class="progress-text">
                        <div class="progress-value" id="reliability-score">65%</div>
                        <div class="progress-label">可靠性</div>
                    </div>
                </div>

                <!-- V4.2 风险预防措施展示 -->
                <div class="risk-prevention-panel">
                    <div class="panel-title">
                        <span>🛡️</span>
                        <span>风险预防措施</span>
                    </div>

                    <div class="prevention-item">
                        <div class="risk-header">
                            <span class="risk-badge critical">CRITICAL</span>
                            <span class="prevention-status prevented">已预防</span>
                        </div>
                        <div class="risk-description">
                            核心决策逻辑与全局护栏存在原则性冲突
                        </div>
                        <div class="prevention-detail">
                            <span class="prevention-label">预防机制:</span>
                            <span>ConstraintPreprocessor在阶段零已阻止此冲突进入知识库</span>
                        </div>
                    </div>

                    <div class="prevention-item">
                        <div class="risk-header">
                            <span class="risk-badge high">HIGH</span>
                            <span class="prevention-status prevented">已预防</span>
                        </div>
                        <div class="risk-description">
                            UserService 未定义明确的响应时间边界条件
                        </div>
                        <div class="prevention-detail">
                            <span class="prevention-label">预防机制:</span>
                            <span>引用式分叉机制自动继承GB001的边界条件</span>
                        </div>
                    </div>
                </div>

                <!-- 文档健康报告 -->
                <div class="health-report-section">
                    <div class="report-title">📋 文档健康报告</div>
                    <div class="report-summary">
                        <div class="summary-item">
                            <span class="summary-label">可靠性评分:</span>
                            <span class="summary-value success">87%</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">预防冲突:</span>
                            <span class="summary-value info">3个</span>
                        </div>
                    </div>
                </div>

                <!-- 报告文件链接 -->
                <div style="margin-top: 1rem;">
                    <a href="#" id="report-link" class="output-link">📄 查看完整报告</a>
                </div>
            </div>
        </div>

        <!-- 区域4：项目经理状态 (PM Status) -->
        <div class="grid-area grid-area-4 vscode-scrollbar">
            <div class="area-title">项目经理状态 (PM Status)</div>
            <div class="area-content">
                <div class="status-item">
                    <span class="status-indicator status-active"></span>
                    <strong>当前项目经理:</strong> <span id="current-pm-role">首席架构师AI</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator status-thinking"></span>
                    <strong>当前任务:</strong> <span id="current-task">从01号文档中识别设计意图并进行实体分类</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator status-active"></span>
                    <strong>工作状态:</strong> <span id="work-status" style="color: #4CAF50;">ACTIVE</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator status-converging"></span>
                    <strong>处理文档:</strong> <span id="current-doc">1-总体架构设计-V2.md</span>
                </div>

                <!-- 任务进度 -->
                <div style="margin-top: 1rem;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                        <span><strong>任务进度:</strong></span>
                        <span style="color: #2196F3; font-weight: bold;">75%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill info" style="width: 75%;"></div>
                    </div>
                </div>

                <div class="status-item" style="margin-top: 0.5rem;">
                    <strong>已处理时间:</strong> <span id="processing-time" style="color: #FF9800;">2分30秒</span>
                </div>
            </div>
        </div>

        <!-- 区域6：项目约束审查 (Constraint Review) -->
        <div class="grid-area grid-area-6 vscode-scrollbar">
            <div class="area-title">项目约束审查 (Constraint Review)</div>
            <div class="area-content" id="constraint-review-content">
                <div style="color: #666; text-align: center; margin-top: 2rem;">
                    点击左侧知识库中的约束节点查看详细的AtomicConstraint结构
                </div>
            </div>
        </div>

        <!-- 区域7：项目知识库 (Knowledge Base) -->
        <div class="grid-area grid-area-7 vscode-scrollbar">
            <div class="area-title">
                项目知识库 (Knowledge Base)
                <span class="help-icon" onclick="showKnowledgeBaseHelp(event)" title="点击查看图例说明">
                    <span class="help-symbol">?</span>
                </span>
            </div>
            <div class="area-content">
                <!-- 知识图谱可视化 -->
                <div class="knowledge-graph" id="knowledge-graph">
                    <!-- V4.2 AtomicConstraint节点 - 展示引用式分叉机制 -->

                    <!-- 根约束节点 -->
                    <div class="constraint-node global" style="top: 30px; left: 30px;"
                         data-category="boundary_condition" data-parent-id=""
                         onclick="showConstraintDetail('GB001')"
                         onmouseenter="showTooltip(this, 'GB001: 全局API响应时间边界条件')"
                         onmouseleave="hideTooltip()">
                        GB001
                        <div class="node-label">边界条件</div>
                    </div>

                    <!-- 分叉约束节点 -->
                    <div class="constraint-node local forked" style="top: 80px; left: 120px;"
                         data-category="constraint" data-parent-id="GB001"
                         onclick="showConstraintDetail('LB002')"
                         onmouseenter="showTooltip(this, 'LB002: 用户服务约束(引用式分叉)')"
                         onmouseleave="hideTooltip()">
                        LB002
                        <div class="node-label">约束(分叉)</div>
                        <div class="fork-indicator">↗ GB001</div>
                    </div>

                    <!-- 另一个分叉约束节点 -->
                    <div class="constraint-node boundary forked" style="top: 40px; left: 200px;"
                         data-category="boundary_condition" data-parent-id="GB001"
                         onclick="showConstraintDetail('BC003')"
                         onmouseenter="showTooltip(this, 'BC003: 核心API响应时间边界(引用式分叉)')"
                         onmouseleave="hideTooltip()">
                        BC003
                        <div class="node-label">边界(分叉)</div>
                        <div class="fork-indicator">↗ GB001</div>
                    </div>

                    <!-- 独立状态机节点 -->
                    <div class="constraint-node state-machine" style="top: 120px; left: 80px;"
                         data-category="state_machine" data-parent-id=""
                         onclick="showConstraintDetail('SM004')"
                         onmouseenter="showTooltip(this, 'SM004: 用户会话状态机验证规则')"
                         onmouseleave="hideTooltip()">
                        SM004
                        <div class="node-label">状态机</div>
                    </div>

                    <!-- 引用关系连接线 (实线) -->
                    <div class="constraint-connection reference" style="top: 55px; left: 75px; width: 50px; transform: rotate(15deg);"></div>

                    <!-- 分叉关系连接线 (虚线) -->
                    <div class="constraint-connection fork" style="top: 55px; left: 75px; width: 130px; transform: rotate(-5deg);"></div>

                    <!-- 提示框 -->
                    <div class="node-tooltip" id="node-tooltip"></div>
                </div>

                <!-- V4.2 AtomicConstraint图例 -->
                <div style="margin-top: 0.8rem; font-size: 0.7rem;">
                    <div style="font-weight: bold; margin-bottom: 0.5rem; color: #0078D4;">AtomicConstraint类型:</div>

                    <div style="margin-bottom: 0.2rem; display: flex; align-items: center;">
                        <span style="color: #FF9800; margin-right: 0.3rem;">●</span>
                        <span>boundary_condition (边界条件)</span>
                    </div>
                    <div style="margin-bottom: 0.2rem; display: flex; align-items: center;">
                        <span style="color: #4CAF50; margin-right: 0.3rem;">●</span>
                        <span>constraint (约束)</span>
                    </div>
                    <div style="margin-bottom: 0.2rem; display: flex; align-items: center;">
                        <span style="color: #F44336; margin-right: 0.3rem;">●</span>
                        <span>guardrail (护栏)</span>
                    </div>
                    <div style="margin-bottom: 0.2rem; display: flex; align-items: center;">
                        <span style="color: #9C27B0; margin-right: 0.3rem;">●</span>
                        <span>state_machine (状态机)</span>
                    </div>

                    <div style="margin-top: 0.5rem; padding-top: 0.5rem; border-top: 1px solid #3C3F41;">
                        <div style="margin-bottom: 0.2rem; display: flex; align-items: center;">
                            <span style="color: #2196F3; margin-right: 0.3rem;">—</span>
                            <span>引用关系 (实线)</span>
                        </div>
                        <div style="margin-bottom: 0.2rem; display: flex; align-items: center;">
                            <span style="color: #FF9800; margin-right: 0.3rem;">- -</span>
                            <span>分叉关系 (虚线)</span>
                        </div>
                    </div>
                </div>

                <!-- 知识库帮助弹窗 -->
                <div class="knowledge-help-popup" id="knowledge-help-popup">
                    <button class="help-close-btn" onclick="hideKnowledgeBaseHelp()">×</button>

                    <div class="help-popup-title">
                        <span>📚</span>
                        <span>V4.2 AtomicConstraint 知识库图例</span>
                    </div>

                    <div class="help-popup-section">
                        <div class="help-section-title">约束类型 (category)</div>
                        <div class="help-legend-item">
                            <span class="help-legend-symbol" style="color: #FF9800;">●</span>
                            <span class="help-legend-text">boundary_condition - 边界条件约束</span>
                        </div>
                        <div class="help-legend-item">
                            <span class="help-legend-symbol" style="color: #4CAF50;">●</span>
                            <span class="help-legend-text">constraint - 一般约束</span>
                        </div>
                        <div class="help-legend-item">
                            <span class="help-legend-symbol" style="color: #F44336;">●</span>
                            <span class="help-legend-text">guardrail - 护栏约束</span>
                        </div>
                        <div class="help-legend-item">
                            <span class="help-legend-symbol" style="color: #9C27B0;">●</span>
                            <span class="help-legend-text">state_machine - 状态机约束</span>
                        </div>
                    </div>

                    <div class="help-popup-section">
                        <div class="help-section-title">关系类型</div>
                        <div class="help-legend-item">
                            <span class="help-legend-symbol" style="color: #2196F3;">—</span>
                            <span class="help-legend-text">引用关系 - 直接引用</span>
                        </div>
                        <div class="help-legend-item">
                            <span class="help-legend-symbol" style="color: #FF9800;">- -</span>
                            <span class="help-legend-text">分叉关系 - 引用式分叉</span>
                        </div>
                    </div>

                    <div class="help-popup-section">
                        <div class="help-section-title">特殊标识</div>
                        <div class="help-legend-item">
                            <span class="help-legend-symbol" style="color: #FF9800;">⚬</span>
                            <span class="help-legend-text">虚线边框 - 分叉约束节点</span>
                        </div>
                        <div class="help-legend-item">
                            <span class="help-legend-symbol" style="color: #FF9800;">↗</span>
                            <span class="help-legend-text">分叉指示器 - 指向父约束</span>
                        </div>
                    </div>

                    <div class="help-popup-section">
                        <div class="help-section-title">交互说明</div>
                        <div style="color: #888; font-size: 0.75rem; line-height: 1.3;">
                            • 点击约束节点查看详细的AtomicConstraint结构<br>
                            • 悬停节点显示约束描述信息<br>
                            • 连接线表示约束间的血统关系
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 区域8：人类输入控制区 -->
        <div class="grid-area grid-area-8">
            <div class="area-content" style="margin-top: 1rem; height: calc(100% - 1rem); display: flex; flex-direction: column;">
                <!-- 详细区 -->
                <div id="detail-area" class="vscode-scrollbar" style="flex: 1; background: #2A2D30; border: 1px solid #3C3F41; border-radius: 4px; margin-bottom: 0.8rem; position: relative; overflow-y: auto;">
                    <!-- 详细区标题（仅在无内容时显示） -->
                    <div id="detail-title" style="position: absolute; top: 4px; left: 8px; background: #3C3F41; color: #BBBBBB; padding: 2px 6px; border-radius: 3px; font-size: 0.7rem; z-index: 10;">详细</div>

                    <!-- 详细区内容 -->
                    <div id="detail-content" style="padding: 0.5rem; font-family: monospace; font-size: 0.8rem; color: #BBBBBB; line-height: 1.3; height: 100%; display: flex; flex-direction: column;">
                        <div id="detail-placeholder" style="color: #666; text-align: center; flex: 1; display: flex; align-items: center; justify-content: center;">点击左侧算法思维日志查看详细内容</div>
                    </div>
                </div>

                <!-- 🚀 V45容器化改造：项目选择器 -->
                <div class="project-selector" style="margin-bottom: 0.5rem; padding: 0.3rem; background: #2A2D30; border: 1px solid #3C3F41; border-radius: 4px;">
                    <div style="display: flex; align-items: center; gap: 0.5rem; font-size: 0.8rem;">
                        <span style="color: #0078D4; font-weight: bold;">🏗️ 项目:</span>
                        <select id="project-selector" style="flex: 1; background: #2A2D30; color: #BBBBBB; border: 1px solid #3C3F41; border-radius: 3px; padding: 0.2rem; font-size: 0.8rem;">
                            <option value="default">默认项目</option>
                        </select>
                        <button id="refresh-projects" onclick="refreshProjects()" style="padding: 0.2rem 0.4rem; background: transparent; color: #0078D4; border: 1px solid #0078D4; border-radius: 3px; cursor: pointer; font-size: 0.7rem;">刷新</button>
                        <button id="new-project" onclick="createNewProject()" style="padding: 0.2rem 0.4rem; background: transparent; color: #4CAF50; border: 1px solid #4CAF50; border-radius: 3px; cursor: pointer; font-size: 0.7rem;">新建</button>
                    </div>
                    <div id="project-status" style="margin-top: 0.3rem; font-size: 0.7rem; color: #666;">
                        <span id="container-status">容器状态: 未连接</span>
                        <span style="margin-left: 1rem;" id="component-count">组件: 0</span>
                    </div>
                </div>

                <!-- 输入框 -->
                <textarea id="user-input" placeholder="基于详细区内容提问或自由输入..." style="width: 100%; height: 60px; background: #2A2D30; color: #BBBBBB; border: 1px solid #3C3F41; border-radius: 4px; padding: 0.5rem; resize: vertical; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-size: 0.9rem; margin-bottom: 0.8rem;"></textarea>

                <!-- 控制按钮（移到最下面） -->
                <div class="control-buttons" style="display: flex; gap: 0.3rem;">
                    <button onclick="startMeeting()" style="flex: 1; padding: 0.3rem; background: transparent; color: #4CAF50; border: 1px solid #4CAF50; border-radius: 4px; cursor: pointer; opacity: 0.7; transition: opacity 0.2s; font-size: 0.8rem;">开始</button>
                    <button onclick="pauseMeeting()" style="flex: 1; padding: 0.3rem; background: transparent; color: #FF9800; border: 1px solid #FF9800; border-radius: 4px; cursor: pointer; opacity: 0.7; transition: opacity 0.2s; font-size: 0.8rem;">暂停</button>
                    <button onclick="stopMeeting()" style="flex: 1; padding: 0.3rem; background: transparent; color: #F44336; border: 1px solid #F44336; border-radius: 4px; cursor: pointer; opacity: 0.7; transition: opacity 0.2s; font-size: 0.8rem;">停止</button>
                    <button id="scanning-button" class="control-btn scanning-btn" onclick="handleScanningClick()" disabled style="flex: 1; padding: 0.3rem; background: transparent; color: #2196F3; border: 1px solid #666; border-radius: 4px; cursor: not-allowed; opacity: 0.7; transition: opacity 0.2s; font-size: 0.8rem;">🔍 扫描</button>
                </div>
            </div>
        </div>

        <!-- 区域9：项目交付结果 (Project Output) -->
        <div class="grid-area grid-area-9 vscode-scrollbar">
            <div class="area-title">项目交付结果 (Project Output)</div>
            <div class="area-content">
                <!-- 整体性审计状态 -->
                <div class="output-status">
                    <div class="output-badge processing" id="audit-status">处理中...</div>
                </div>

                <!-- 产出链接 -->
                <div class="output-links" id="output-links" style="display: none;">
                    <a href="#" class="output-link">📦 最终产出物 (代码)</a>
                    <a href="#" class="output-link">📋 全流程总结报告</a>
                    <a href="#" class="output-link">🔍 详细审计报告</a>
                </div>

                <!-- 完成统计 -->
                <div style="margin-top: 1rem; font-size: 0.8rem;">
                    <div style="margin-bottom: 0.5rem; font-weight: bold;">处理统计:</div>

                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>文档数量</span>
                            <span style="color: #2196F3; font-weight: bold;" id="processed-docs">3/5</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill info" style="width: 60%;"></div>
                        </div>
                    </div>

                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>约束数量</span>
                            <span style="color: #4CAF50; font-weight: bold;" id="total-constraints">25</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: 83%;"></div>
                        </div>
                    </div>

                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>风险数量</span>
                            <span style="color: #FF9800; font-weight: bold;" id="total-risks">2</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill warning" style="width: 40%;"></div>
                        </div>
                    </div>

                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>处理时间</span>
                            <span style="color: #9C27B0; font-weight: bold;" id="total-time">150秒</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%; background: linear-gradient(90deg, #9C27B0, #BA68C8);"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- V2 External JS -->
    <script src="{{ url_for('static', filename='js/project_manager_v2_app.js') }}"></script>
</body>
</html>
