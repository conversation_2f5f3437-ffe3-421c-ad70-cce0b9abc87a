<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V2项目经理工作台 - 虚拟项目经理交互版</title>
    
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    
    <!-- 复用nine_grid的基础样式 -->
    <link rel="stylesheet" href="/static/css/nine_grid_base.css">
    
    <style>
        /* V2特有样式 - 复用nine_grid的基础样式 */
        
        /* V2特有样式 - 复用nine_grid的基础样式 */
        
        /* V2知识库可视化样式 */
        .knowledge-graph {
            width: 100%;
            height: 200px;
            background: #2D2D30;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }

        .constraint-node {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.6rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid;
        }

        .constraint-node.global {
            background: #4CAF50;
            border-color: #4CAF50;
            color: white;
        }

        .constraint-node.local {
            background: #2196F3;
            border-color: #2196F3;
            color: white;
        }

        .constraint-node.boundary {
            background: #FF9800;
            border-color: #FF9800;
            color: white;
        }

        .constraint-node.state-machine {
            background: #9C27B0;
            border-color: #9C27B0;
            color: white;
        }

        .constraint-node:hover {
            transform: scale(1.1);
            z-index: 10;
        }

        .constraint-connection {
            position: absolute;
            height: 2px;
            background: #666;
            transform-origin: left center;
        }

        .constraint-connection.fork {
            background: #FF9800;
            height: 3px;
        }

        /* V2风险评估仪表盘 */
        .risk-dashboard {
            background: #2D2D30;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 0.8rem;
        }

        .risk-score {
            text-align: center;
            margin-bottom: 0.5rem;
        }

        .risk-score-value {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .risk-score.critical .risk-score-value {
            color: #F44336;
        }

        .risk-score.high .risk-score-value {
            color: #FF9800;
        }

        .risk-score.medium .risk-score-value {
            color: #FFC107;
        }

        .risk-score.low .risk-score-value {
            color: #4CAF50;
        }

        .risk-score-label {
            font-size: 0.8rem;
            color: #BBBBBB;
            margin-top: 0.2rem;
        }

        /* V2四阶段流程进度 */
        .stage-progress {
            margin-bottom: 1rem;
        }

        .stage-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            padding: 0.3rem;
            border-radius: 3px;
            transition: background-color 0.2s ease;
        }

        .stage-item.current {
            background: rgba(0, 120, 212, 0.1);
            border-left: 3px solid #0078D4;
        }

        .stage-item.completed {
            background: rgba(76, 175, 80, 0.1);
            border-left: 3px solid #4CAF50;
        }

        .stage-item.pending {
            background: rgba(158, 158, 158, 0.1);
            border-left: 3px solid #9E9E9E;
        }

        .stage-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
            flex-shrink: 0;
        }

        .stage-indicator.current {
            background: #0078D4;
            animation: pulse 2s infinite;
        }

        .stage-indicator.completed {
            background: #4CAF50;
        }

        .stage-indicator.pending {
            background: #9E9E9E;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* V2决策中心卡片 */
        .decision-card {
            background: #2D2D30;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
            border-left: 4px solid;
        }

        .decision-card.insight {
            border-left-color: #2196F3;
        }

        .decision-card.action {
            border-left-color: #FF9800;
        }

        .decision-card.log {
            border-left-color: #9E9E9E;
        }

        .decision-card-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #0078D4;
        }

        .decision-options {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .decision-btn {
            padding: 0.4rem 0.8rem;
            background: transparent;
            border: 1px solid #0078D4;
            color: #0078D4;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .decision-btn:hover {
            background: #0078D4;
            color: white;
        }

        .decision-btn.approve {
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .decision-btn.approve:hover {
            background: #4CAF50;
            color: white;
        }

        .decision-btn.reject {
            border-color: #F44336;
            color: #F44336;
        }

        .decision-btn.reject:hover {
            background: #F44336;
            color: white;
        }

        /* V2项目输出样式 */
        .output-status {
            text-align: center;
            margin-bottom: 0.8rem;
        }

        .output-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .output-badge.success {
            background: #4CAF50;
            color: white;
        }

        .output-badge.processing {
            background: #FF9800;
            color: white;
            animation: processingPulse 1s infinite;
        }

        .output-badge.error {
            background: #F44336;
            color: white;
        }

        .output-links {
            margin-top: 0.8rem;
        }

        .output-link {
            display: block;
            padding: 0.5rem;
            background: #393B40;
            color: #0078D4;
            text-decoration: none;
            border-radius: 3px;
            margin-bottom: 0.3rem;
            transition: background-color 0.2s ease;
        }

        .output-link:hover {
            background: #0078D4;
            color: white;
        }

        @keyframes processingPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* V2约束详情样式 */
        .constraint-detail {
            background: #2D2D30;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
        }

        .constraint-detail-header {
            font-weight: bold;
            color: #0078D4;
            margin-bottom: 0.5rem;
            border-bottom: 1px solid #3C3F41;
            padding-bottom: 0.3rem;
        }

        .constraint-detail-item {
            margin-bottom: 0.3rem;
            font-size: 0.9rem;
        }

        .constraint-detail-item strong {
            color: #BBBBBB;
        }

        .constraint-params {
            background: #1E1F22;
            padding: 0.5rem;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.7rem;
            margin-top: 0.3rem;
        }

        /* 8号区域详细区样式 */
        #detail-area {
            position: relative;
        }

        #detail-title {
            position: absolute;
            top: 4px;
            left: 8px;
            background: #3C3F41;
            color: #BBBBBB;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.7rem;
            z-index: 10;
        }

        #detail-content {
            padding: 0.5rem;
            font-family: monospace;
            font-size: 0.8rem;
            color: #BBBBBB;
            line-height: 1.3;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        #detail-placeholder {
            color: #666;
            text-align: center;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 项目选择器样式 */
        .project-selector {
            margin-bottom: 0.5rem;
            padding: 0.3rem;
            background: #2A2D30;
            border: 1px solid #3C3F41;
            border-radius: 4px;
        }

        .project-selector select {
            flex: 1;
            background: #2A2D30;
            color: #BBBBBB;
            border: 1px solid #3C3F41;
            border-radius: 3px;
            padding: 0.2rem;
            font-size: 0.8rem;
        }

        .project-selector button {
            padding: 0.2rem 0.4rem;
            background: transparent;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.7rem;
            transition: opacity 0.2s;
        }

        .project-selector button:hover {
            opacity: 1;
        }



        /* V2知识库可视化样式 */
        .knowledge-graph {
            width: 100%;
            height: 200px;
            background: #2D2D30;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }

        .constraint-node {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.6rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid;
        }

        .constraint-node.global {
            background: #4CAF50;
            border-color: #4CAF50;
            color: white;
        }

        .constraint-node.local {
            background: #2196F3;
            border-color: #2196F3;
            color: white;
        }

        .constraint-node.boundary {
            background: #FF9800;
            border-color: #FF9800;
            color: white;
        }

        .constraint-node.state-machine {
            background: #9C27B0;
            border-color: #9C27B0;
            color: white;
        }

        .constraint-node:hover {
            transform: scale(1.1);
            z-index: 10;
        }

        /* V2风险评分仪表盘 */
        .risk-dashboard {
            background: #2D2D30;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 0.8rem;
        }

        .risk-score {
            text-align: center;
            margin-bottom: 0.5rem;
        }

        .risk-score-value {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .risk-score.critical .risk-score-value {
            color: #F44336;
        }

        .risk-score.high .risk-score-value {
            color: #FF9800;
        }

        .risk-score.medium .risk-score-value {
            color: #FFC107;
        }

        .risk-score.low .risk-score-value {
            color: #4CAF50;
        }

        .risk-score-label {
            font-size: 0.7rem;
            color: #BBBBBB;
        }

        /* V2四阶段流程进度 */
        .stage-progress {
            margin-bottom: 0.8rem;
        }

        .stage-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.3rem;
            padding: 0.3rem;
            border-radius: 3px;
            transition: background-color 0.2s ease;
        }

        .stage-item.current {
            background: #0078D4;
            color: white;
        }

        .stage-item.completed {
            background: #4CAF50;
            color: white;
        }

        .stage-item.pending {
            background: #666;
            color: #999;
        }

        .stage-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .stage-indicator.current {
            background: #0078D4;
            animation: pulse 2s infinite;
        }

        .stage-indicator.completed {
            background: #4CAF50;
        }

        .stage-indicator.pending {
            background: #666;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* V2决策中心卡片样式 */
        .decision-card {
            background: #393B40;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 0.8rem;
            border-left: 4px solid;
        }

        .decision-card.insight {
            border-left-color: #4CAF50;
        }

        .decision-card.action {
            border-left-color: #FF9800;
        }

        .decision-card.log {
            border-left-color: #2196F3;
        }

        .decision-card-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .decision-options {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .decision-btn {
            padding: 0.4rem 0.8rem;
            background: transparent;
            border: 1px solid #0078D4;
            color: #0078D4;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .decision-btn:hover {
            background: #0078D4;
            color: white;
        }

        .decision-btn.approve {
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .decision-btn.approve:hover {
            background: #4CAF50;
            color: white;
        }

        .decision-btn.reject {
            border-color: #F44336;
            color: #F44336;
        }

        .decision-btn.reject:hover {
            background: #F44336;
            color: white;
        }

        /* V2输入区域样式 */
        .input-section {
            margin-bottom: 0.8rem;
        }

        .input-field {
            width: 100%;
            background: #3C3F41;
            color: #BBBBBB;
            border: 1px solid #555;
            padding: 0.5rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .input-field:focus {
            outline: none;
            border-color: #0078D4;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            flex: 1;
            padding: 0.5rem;
            background: transparent;
            border: 1px solid;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .action-btn.primary {
            border-color: #0078D4;
            color: #0078D4;
        }

        .action-btn.primary:hover {
            background: #0078D4;
            color: white;
        }

        .action-btn.secondary {
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .action-btn.secondary:hover {
            background: #4CAF50;
            color: white;
        }

        .action-btn.tertiary {
            border-color: #666;
            color: #666;
        }

        .action-btn.tertiary:hover {
            background: #666;
            color: white;
        }

        .action-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
        }

        /* V2项目输出样式 */
        .output-status {
            text-align: center;
            margin-bottom: 0.8rem;
        }

        .output-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .output-badge.success {
            background: #4CAF50;
            color: white;
        }

        .output-badge.processing {
            background: #FF9800;
            color: white;
            animation: processingPulse 1s infinite;
        }

        .output-badge.error {
            background: #F44336;
            color: white;
        }

        .output-links {
            margin-top: 0.8rem;
        }

        .output-link {
            display: block;
            padding: 0.5rem;
            background: #393B40;
            color: #0078D4;
            text-decoration: none;
            border-radius: 3px;
            margin-bottom: 0.3rem;
            transition: background-color 0.2s ease;
        }

        .output-link:hover {
            background: #0078D4;
            color: white;
        }

        @keyframes processingPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <!-- 左侧菜单感应区域 -->
    <div class="left-menu-trigger" id="leftMenuTrigger"></div>
    
    <!-- 自动隐藏左侧菜单 -->
    <nav class="left-menu" id="leftMenu">
        <div class="menu-title">🏗️ V2项目经理工作台</div>
        <a href="/" class="menu-item" data-target="home">🏠 主页</a>
        <a href="/nine-grid" class="menu-item" data-target="nine-grid">🔧 九宫格系统</a>
        <a href="/debug" class="menu-item" data-target="debug">🐛 调试中心</a>
        <a href="/api/status" class="menu-item" data-target="status" target="_blank">📊 系统状态</a>
        <a href="/api/health" class="menu-item" data-target="health" target="_blank">❤️ 健康检查</a>
        <div class="menu-item" onclick="openConfigCenter()">⚙️ 配置中心</div>
    </nav>

    <!-- 九宫格主容器 -->
    <div class="nine-grid-container">
        <!-- 区域1-2：项目进度监控 (Process Overview) -->
        <div class="grid-area grid-area-1-2 vscode-scrollbar">
            <div class="area-title">项目进度监控 (Process Overview)</div>
            <div class="area-content">
                <!-- V2四阶段流程进度 -->
                <div class="stage-progress">
                    <div class="stage-item current">
                        <div class="stage-indicator current"></div>
                        <span>阶段零：标准化与预验证</span>
                    </div>
                    <div class="stage-item pending">
                        <div class="stage-indicator pending"></div>
                        <span>阶段一：全局契约生成</span>
                    </div>
                    <div class="stage-item pending">
                        <div class="stage-indicator pending"></div>
                        <span>阶段二：引用式契约生成</span>
                    </div>
                    <div class="stage-item pending">
                        <div class="stage-indicator pending"></div>
                        <span>阶段三：契约履行与审计</span>
                    </div>
                    <div class="stage-item pending">
                        <div class="stage-indicator pending"></div>
                        <span>阶段四：整体性审计</span>
                    </div>
                </div>

                <!-- 关键指标统计 -->
                <div style="margin-top: 1rem;">
                    <div style="font-weight: bold; margin-bottom: 0.5rem;">关键指标:</div>
                    <div style="margin-bottom: 0.3rem;">已发现原子约束: <span id="atomic-constraints-count">25</span></div>
                    <div style="margin-bottom: 0.3rem;">已生成全局契约点: <span id="global-contracts-count">18</span></div>
                    <div style="margin-bottom: 0.3rem;">已处理文档: <span id="processed-docs-count">3</span>/5</div>
                    <div style="margin-bottom: 0.3rem;">当前可靠性评分: <span id="current-reliability-score">87.7%</span></div>
                </div>

                <!-- 进度条 -->
                <div style="margin-top: 1rem;">
                    <div style="margin-bottom: 0.3rem;">整体进度:</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="overall-progress" style="width: 25%;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 区域5：项目经理决策中心 (Decision Center) -->
        <div class="grid-area grid-area-5 vscode-scrollbar">
            <div class="area-title">项目经理决策中心 (Decision Center)</div>
            <div class="area-content">
                <!-- 关键洞察 -->
                <div class="decision-card insight">
                    <div class="decision-card-title">🔍 关键洞察</div>
                    <div id="key-insight">发现3个致命级架构风险，建议项目经理审查。</div>
                </div>

                <!-- 需要决策 -->
                <div class="decision-card action" id="action-required" style="display: none;">
                    <div class="decision-card-title">⚠️ 需要项目经理决策</div>
                    <div id="decision-question">项目可靠性评分较低(0.65)，且发现致命级风险。是否继续？</div>
                    <div class="decision-options">
                        <button class="decision-btn approve" onclick="makeDecision('approve')">批准并继续项目</button>
                        <button class="decision-btn reject" onclick="makeDecision('reject')">暂停项目检测</button>
                    </div>
                </div>

                <!-- 决策日志 -->
                <div class="decision-card log">
                    <div class="decision-card-title">📋 项目经理决策日志</div>
                    <div id="decision-log" style="font-family: monospace; font-size: 0.8rem; max-height: 200px; overflow-y: auto;">
                        <div>[Scheduler] 正在调度 'Standardization' 蓝图...</div>
                        <div>[Blueprint:Standardize] 开始分析 1-总体架构设计-V2.md...</div>
                        <div>[Blueprint:Standardize] 成功提取'boundary_condition'类别的原子约束 [GB001]。</div>
                        <div>[Scheduler] 'Standardization' 蓝图执行完毕。</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 区域3：项目风险评估 (Risk Assessment) -->
        <div class="grid-area grid-area-3 vscode-scrollbar">
            <div class="area-title">项目风险评估 (Risk Assessment)</div>
            <div class="area-content">
                <!-- 可靠性评分仪表盘 -->
                <div class="risk-dashboard">
                    <div class="risk-score critical" id="risk-score">
                        <div class="risk-score-value">65%</div>
                        <div class="risk-score-label">项目可靠性评分</div>
                    </div>
                </div>

                <!-- 关键风险列表 -->
                <div style="margin-bottom: 0.8rem;">
                    <div style="font-weight: bold; margin-bottom: 0.5rem;">关键风险列表:</div>
                    <div id="risk-list">
                        <div style="color: #F44336; margin-bottom: 0.3rem;">[CRITICAL] 核心决策逻辑与全局护栏存在原则性冲突。</div>
                        <div style="color: #FF9800; margin-bottom: 0.3rem;">[HIGH] UserService 未定义明确的响应时间边界条件。</div>
                    </div>
                </div>

                <!-- 报告文件链接 -->
                <div style="margin-top: 1rem;">
                    <a href="#" id="report-link" class="output-link">📄 查看完整报告</a>
                </div>
            </div>
        </div>

        <!-- 区域4：项目经理状态 (PM Status) -->
        <div class="grid-area grid-area-4 vscode-scrollbar">
            <div class="area-title">项目经理状态 (PM Status)</div>
            <div class="area-content">
                <div class="status-item">
                    <span class="status-indicator status-active"></span>
                    <strong>当前项目经理:</strong> <span id="current-pm-role">首席架构师AI</span>
                </div>
                <div class="status-item">
                    <strong>当前任务:</strong> <span id="current-task">从01号文档中识别设计意图并进行实体分类</span>
                </div>
                <div class="status-item">
                    <strong>工作状态:</strong> <span id="work-status">ACTIVE</span>
                </div>
                <div class="status-item">
                    <strong>处理文档:</strong> <span id="current-doc">1-总体架构设计-V2.md</span>
                </div>
                <div class="status-item">
                    <strong>已处理时间:</strong> <span id="processing-time">2分30秒</span>
                </div>
            </div>
        </div>

        <!-- 区域6：项目约束审查 (Constraint Review) -->
        <div class="grid-area grid-area-6 vscode-scrollbar">
            <div class="area-title">项目约束审查 (Constraint Review)</div>
            <div class="area-content" id="constraint-review-content">
                <div style="color: #666; text-align: center; margin-top: 2rem;">
                    点击左侧知识库中的约束以查看详情
                </div>
            </div>
        </div>

        <!-- 区域7：项目知识库 (Knowledge Base) -->
        <div class="grid-area grid-area-7 vscode-scrollbar">
            <div class="area-title">项目知识库 (Knowledge Base)</div>
            <div class="area-content">
                <!-- 知识图谱可视化 -->
                <div class="knowledge-graph" id="knowledge-graph">
                    <!-- 约束节点将通过JS动态生成 -->
                </div>
                
                <!-- 图例 -->
                <div style="margin-top: 0.8rem; font-size: 0.7rem;">
                    <div style="margin-bottom: 0.2rem;"><span style="color: #4CAF50;">●</span> 全局约束</div>
                    <div style="margin-bottom: 0.2rem;"><span style="color: #2196F3;">●</span> 本地约束</div>
                    <div style="margin-bottom: 0.2rem;"><span style="color: #FF9800;">●</span> 边界条件</div>
                    <div style="margin-bottom: 0.2rem;"><span style="color: #9C27B0;">●</span> 状态机</div>
                </div>
            </div>
        </div>

        <!-- 区域8：用户输入区 (User Input Area) -->
        <div class="grid-area grid-area-8">
            <div class="area-content" style="margin-top: 1rem; height: calc(100% - 1rem); display: flex; flex-direction: column;">
                <!-- 详细区 -->
                <div id="detail-area" class="vscode-scrollbar" style="flex: 1; background: #2A2D30; border: 1px solid #3C3F41; border-radius: 4px; margin-bottom: 0.8rem; position: relative; overflow-y: auto;">
                    <!-- 详细区标题（仅在无内容时显示） -->
                    <div id="detail-title" style="position: absolute; top: 4px; left: 8px; background: #3C3F41; color: #BBBBBB; padding: 2px 6px; border-radius: 3px; font-size: 0.7rem; z-index: 10;">详细</div>

                    <!-- 详细区内容 -->
                    <div id="detail-content" style="padding: 0.5rem; font-family: monospace; font-size: 0.8rem; color: #BBBBBB; line-height: 1.3; height: 100%; display: flex; flex-direction: column;">
                        <div id="detail-placeholder" style="color: #666; text-align: center; flex: 1; display: flex; align-items: center; justify-content: center;">点击左侧知识库中的约束以查看详细内容</div>
                    </div>
                </div>

                <!-- 项目选择器 -->
                <div class="project-selector" style="margin-bottom: 0.5rem; padding: 0.3rem; background: #2A2D30; border: 1px solid #3C3F41; border-radius: 4px;">
                    <div style="display: flex; align-items: center; gap: 0.5rem; font-size: 0.8rem;">
                        <span style="color: #0078D4; font-weight: bold;">🏗️ 项目:</span>
                        <select id="project-selector" style="flex: 1; background: #2A2D30; color: #BBBBBB; border: 1px solid #3C3F41; border-radius: 3px; padding: 0.2rem; font-size: 0.8rem;">
                            <option value="default">默认项目</option>
                        </select>
                        <button id="refresh-projects" onclick="refreshProjects()" style="padding: 0.2rem 0.4rem; background: transparent; color: #0078D4; border: 1px solid #0078D4; border-radius: 3px; cursor: pointer; font-size: 0.7rem;">刷新</button>
                        <button id="new-project" onclick="createNewProject()" style="padding: 0.2rem 0.4rem; background: transparent; color: #4CAF50; border: 1px solid #4CAF50; border-radius: 3px; cursor: pointer; font-size: 0.7rem;">新建</button>
                    </div>
                    <div id="project-status" style="margin-top: 0.3rem; font-size: 0.7rem; color: #666;">
                        <span id="container-status">容器状态: 未连接</span>
                        <span style="margin-left: 1rem;" id="component-count">组件: 0</span>
                    </div>
                </div>

                <!-- 输入框 -->
                <textarea id="user-input" placeholder="基于详细区内容提问或自由输入..." style="width: 100%; height: 60px; background: #2A2D30; color: #BBBBBB; border: 1px solid #3C3F41; border-radius: 4px; padding: 0.5rem; resize: vertical; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-size: 0.9rem; margin-bottom: 0.8rem;"></textarea>

                <!-- 控制按钮（移到最下面） -->
                <div class="control-buttons" style="display: flex; gap: 0.3rem;">
                    <button id="start-check-btn" onclick="startCheck()" style="flex: 1; padding: 0.3rem; background: transparent; color: #4CAF50; border: 1px solid #4CAF50; border-radius: 4px; cursor: pointer; opacity: 0.7; transition: opacity 0.2s; font-size: 0.8rem;">检查</button>
                    <button id="start-governance-btn" onclick="startGovernance()" style="flex: 1; padding: 0.3rem; background: transparent; color: #FF9800; border: 1px solid #FF9800; border-radius: 4px; cursor: pointer; opacity: 0.7; transition: opacity 0.2s; font-size: 0.8rem;">治理</button>
                    <button id="generate-code-btn" onclick="generateCode()" disabled style="flex: 1; padding: 0.3rem; background: transparent; color: #F44336; border: 1px solid #666; border-radius: 4px; cursor: not-allowed; opacity: 0.7; transition: opacity 0.2s; font-size: 0.8rem;">生成代码</button>
                    <button id="doc-path-input" class="control-btn" onclick="setDocPath()" style="flex: 1; padding: 0.3rem; background: transparent; color: #2196F3; border: 1px solid #2196F3; border-radius: 4px; cursor: pointer; opacity: 0.7; transition: opacity 0.2s; font-size: 0.8rem;">📁 路径</button>
                </div>
            </div>
        </div>

        <!-- 区域9：项目交付结果 (Project Output) -->
        <div class="grid-area grid-area-9 vscode-scrollbar">
            <div class="area-title">项目交付结果 (Project Output)</div>
            <div class="area-content">
                <!-- 整体性审计状态 -->
                <div class="output-status">
                    <div class="output-badge processing" id="audit-status">处理中...</div>
                </div>

                <!-- 产出链接 -->
                <div class="output-links" id="output-links" style="display: none;">
                    <a href="#" class="output-link">📦 最终产出物 (代码)</a>
                    <a href="#" class="output-link">📋 全流程总结报告</a>
                    <a href="#" class="output-link">🔍 详细审计报告</a>
                </div>

                <!-- 完成统计 -->
                <div style="margin-top: 1rem; font-size: 0.8rem;">
                    <div style="margin-bottom: 0.3rem;">处理统计:</div>
                    <div style="margin-bottom: 0.2rem;">文档数量: <span id="processed-docs">0</span></div>
                    <div style="margin-bottom: 0.2rem;">约束数量: <span id="total-constraints">0</span></div>
                    <div style="margin-bottom: 0.2rem;">风险数量: <span id="total-risks">0</span></div>
                    <div style="margin-bottom: 0.2rem;">处理时间: <span id="total-time">0秒</span></div>
                </div>
            </div>
        </div>
    </div>

    <!-- V2 External JS -->
    <script src="{{ url_for('static', filename='js/project_manager_v2_app.js') }}"></script>
</body>
</html>
