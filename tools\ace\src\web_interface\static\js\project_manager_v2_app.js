document.addEventListener('DOMContentLoaded', function() {
    console.log("V2 Project Manager UI Initialized");

    // Socket.IO连接
    const socket = io();

    // --- V2数据模型 ---

    const mockHealthReport = {
        reliability_score: 0.65,
        detected_risks: [
            {
                risk_id: "R001",
                risk_level: "CRITICAL",
                risk_type: "PRINCIPLE_CONFLICT",
                description: "核心决策逻辑与全局护栏存在原则性冲突。",
                suggestion: "建议人工审查并修正 'decision_nexus_kernel' 节点的设计原则。"
            },
            {
                risk_id: "R002",
                risk_level: "HIGH",
                risk_type: "MISSING_BOUNDARY_OR_STATE_ANALYSIS",
                description: "UserService 未定义明确的响应时间边界条件。",
                suggestion: "为UserService补充性能边界约束。"
            }
        ],
        report_path: "/path/to/检查修改报告提示词.md"
    };

    const mockKnowledgeBase = [
        {
            id: "GB001",
            parent_id: null,
            category: "boundary_condition",
            type: "response_time",
            params: { "max_ms": 1000 },
            description: "所有面向用户的API必须在1000ms内响应。"
        },
        {
            id: "LC001",
            parent_id: "GB001", // Forked constraint
            category: "boundary_condition",
            type: "response_time",
            params: { "max_ms": 200 },
            description: "登录API作为核心接口，必须在200ms内响应。"
        },
        {
            id: "GS001",
            parent_id: null,
            category: "state_machine",
            type: "user_account_lifecycle",
            params: { "diagram_source": "stateDiagram-v2\\n [*] --> ACTIVE\\n ACTIVE --> SUSPENDED" },
            description: "定义用户账户的基础生命周期。"
        },
        {
            id: "GC001",
            parent_id: null,
            category: "constraint",
            type: "authentication_requirement",
            params: {},
            description: "所有公共API必须进行身份验证。"
        }
    ];

    const mockPMStatus = {
        current_role: "首席架构师AI",
        current_task: "从01号文档中识别设计意图并进行实体分类",
        work_status: "ACTIVE",
        current_doc: "1-总体架构设计-V2.md",
        processing_time: "2分30秒"
    };
    
    const mockProcessOverview = {
        current_stage_index: 0, // 0: Stage 0, 1: Stage 1, etc.
        stages: ["阶段零：标准化", "阶段一：全局契约", "阶段二：引用式契约", "阶段三：契约履行", "阶段四：整体审计"],
        metrics: {
            atomic_constraints: 25,
            global_contracts: 18,
            processed_docs: 3,
            total_docs: 5,
            reliability_score: 87.7
        }
    };

    const mockDecisionCenterLog = [
        "[Scheduler] 正在调度 'Standardization' 蓝图...",
        "[Blueprint:Standardize] 开始分析 1-总体架构设计-V2.md...",
        "[Blueprint:Standardize] 成功提取'boundary_condition'类别的原子约束 [GB001]。",
        "[Scheduler] 'Standardization' 蓝图执行完毕。"
    ];

    const mockProjectOutput = {
        audit_status: "处理中...",
        download_links: [
            { name: "最终产出物 (代码)", url: "#" },
            { name: "全流程总结报告", url: "#" },
            { name: "详细审计报告", url: "#" }
        ],
        stats: {
            processed_docs: 0,
            total_constraints: 0,
            total_risks: 0,
            total_time: "0秒"
        }
    };

    // --- 渲染函数 ---

    function renderRiskAssessment(report) {
        const riskScoreElement = document.getElementById('risk-score');
        const riskListElement = document.getElementById('risk-list');
        const reportLinkElement = document.getElementById('report-link');

        // 更新风险评分
        const scoreValue = Math.round(report.reliability_score * 100);
        riskScoreElement.querySelector('.risk-score-value').textContent = scoreValue + '%';
        
        // 更新风险等级样式
        riskScoreElement.className = 'risk-score ' + getRiskLevelClass(report.reliability_score);
        
        // 更新风险列表
        let riskHtml = '';
        report.detected_risks.forEach(risk => {
            const color = getRiskColor(risk.risk_level);
            riskHtml += `<div style="color: ${color}; margin-bottom: 0.3rem;">[${risk.risk_level}] ${risk.description}</div>`;
        });
        riskListElement.innerHTML = riskHtml;
        
        // 更新报告链接
        reportLinkElement.href = report.report_path;
    }

    function renderKnowledgeBase(constraints) {
        const graphElement = document.getElementById('knowledge-graph');
        graphElement.innerHTML = '';
        
        // 计算节点位置
        const positions = calculateNodePositions(constraints);
        
        // 创建约束节点
        constraints.forEach((constraint, index) => {
            const node = document.createElement('div');
            node.className = `constraint-node ${constraint.category.replace('_', '-')}`;
            node.style.left = positions[index].x + 'px';
            node.style.top = positions[index].y + 'px';
            node.textContent = constraint.id;
            node.title = constraint.description;
            node.dataset.id = constraint.id;
            
            node.addEventListener('click', () => {
                renderConstraintReview(constraint);
            });
            
            graphElement.appendChild(node);
        });
        
        // 创建连接线
        constraints.forEach(constraint => {
            if (constraint.parent_id) {
                const parentNode = constraints.find(c => c.id === constraint.parent_id);
                if (parentNode) {
                    createConnection(parentNode, constraint, graphElement);
                }
            }
        });
    }

    function renderConstraintReview(constraint) {
        const contentDiv = document.getElementById('constraint-review-content');
        const detailContent = document.getElementById('detail-content');
        
        if (!constraint) {
            const placeholderText = '点击左侧知识库中的约束以查看详情';
            contentDiv.innerHTML = `<div style="color: #666; text-align: center; margin-top: 2rem;">${placeholderText}</div>`;
            if (detailContent) {
                detailContent.innerHTML = `<div id="detail-placeholder" style="color: #666; text-align: center; flex: 1; display: flex; align-items: center; justify-content: center;">${placeholderText}</div>`;
            }
            return;
        }
        
        let html = `
            <div class="constraint-detail">
                <div class="constraint-detail-header">约束详情</div>
                <div class="constraint-detail-item"><strong>ID:</strong> ${constraint.id}</div>
                <div class="constraint-detail-item"><strong>Parent ID:</strong> ${constraint.parent_id || 'N/A'}</div>
                <div class="constraint-detail-item"><strong>Category:</strong> ${constraint.category}</div>
                <div class="constraint-detail-item"><strong>Type:</strong> ${constraint.type}</div>
                <div class="constraint-detail-item"><strong>Description:</strong> ${constraint.description}</div>
                <div class="constraint-detail-item"><strong>Params:</strong></div>
                <div class="constraint-params">${JSON.stringify(constraint.params, null, 2)}</div>
            </div>
        `;
        contentDiv.innerHTML = html;
        
        // 同时更新详细区内容
        if (detailContent) {
            detailContent.innerHTML = `
                <div style="color: #0078D4; font-weight: bold; margin-bottom: 0.5rem;">🔍 约束详情</div>
                <div style="margin-bottom: 0.3rem;">
                    <strong>ID:</strong> ${constraint.id}
                </div>
                <div style="margin-bottom: 0.3rem;">
                    <strong>Parent ID:</strong> ${constraint.parent_id || 'N/A'}
                </div>
                <div style="margin-bottom: 0.3rem;">
                    <strong>Category:</strong> ${constraint.category}
                </div>
                <div style="margin-bottom: 0.3rem;">
                    <strong>Type:</strong> ${constraint.type}
                </div>
                <div style="margin-bottom: 0.3rem;">
                    <strong>Description:</strong> ${constraint.description}
                </div>
                <div style="margin-bottom: 0.3rem;">
                    <strong>Params:</strong>
                </div>
                <div style="background: #1E1F22; padding: 0.3rem; border-radius: 3px; font-family: monospace; font-size: 0.7rem; margin-top: 0.2rem;">
                    ${JSON.stringify(constraint.params, null, 2)}
                </div>
            `;
        }
    }

    function renderPMStatus(status) {
        document.getElementById('current-pm-role').textContent = status.current_role;
        document.getElementById('current-task').textContent = status.current_task;
        document.getElementById('work-status').textContent = status.work_status;
        document.getElementById('current-doc').textContent = status.current_doc;
        document.getElementById('processing-time').textContent = status.processing_time;
    }
    
    function renderProcessOverview(overview) {
        // 更新阶段进度
        const stageItems = document.querySelectorAll('.stage-item');
        stageItems.forEach((item, index) => {
            item.className = 'stage-item ' + getStageClass(index, overview.current_stage_index);
            const indicator = item.querySelector('.stage-indicator');
            indicator.className = 'stage-indicator ' + getStageClass(index, overview.current_stage_index);
        });
        
        // 更新指标
        document.getElementById('atomic-constraints-count').textContent = overview.metrics.atomic_constraints;
        document.getElementById('global-contracts-count').textContent = overview.metrics.global_contracts;
        document.getElementById('processed-docs-count').textContent = overview.metrics.processed_docs + '/' + overview.metrics.total_docs;
        document.getElementById('current-reliability-score').textContent = overview.metrics.reliability_score + '%';
        
        // 更新进度条
        const progress = (overview.current_stage_index / (overview.stages.length - 1)) * 100;
        document.getElementById('overall-progress').style.width = progress + '%';
    }

    function renderDecisionCenter(logs) {
        const logElement = document.getElementById('decision-log');
        let logHtml = logs.map(log => `<div>${log}</div>`).join('');
        logElement.innerHTML = logHtml;
        logElement.scrollTop = logElement.scrollHeight;
    }

    function renderProjectOutput(output) {
        const auditStatusElement = document.getElementById('audit-status');
        const outputLinksElement = document.getElementById('output-links');
        
        // 更新审计状态
        auditStatusElement.textContent = output.audit_status;
        auditStatusElement.className = 'output-badge ' + getAuditStatusClass(output.audit_status);
        
        // 更新产出链接
        if (output.download_links && output.download_links.length > 0) {
            let linksHtml = output.download_links.map(link => 
                `<a href="${link.url}" class="output-link">${link.name}</a>`
            ).join('');
            outputLinksElement.innerHTML = linksHtml;
            outputLinksElement.style.display = 'block';
        }
        
        // 更新统计
        document.getElementById('processed-docs').textContent = output.stats.processed_docs;
        document.getElementById('total-constraints').textContent = output.stats.total_constraints;
        document.getElementById('total-risks').textContent = output.stats.total_risks;
        document.getElementById('total-time').textContent = output.stats.total_time;
    }

    // --- 辅助函数 ---

    function getRiskLevelClass(score) {
        if (score < 0.5) return 'critical';
        if (score < 0.7) return 'high';
        if (score < 0.85) return 'medium';
        return 'low';
    }

    function getRiskColor(level) {
        switch (level) {
            case 'CRITICAL': return '#F44336';
            case 'HIGH': return '#FF9800';
            case 'MEDIUM': return '#FFC107';
            case 'LOW': return '#4CAF50';
            default: return '#BBBBBB';
        }
    }

    function getStageClass(index, currentIndex) {
        if (index < currentIndex) return 'completed';
        if (index === currentIndex) return 'current';
        return 'pending';
    }

    function getAuditStatusClass(status) {
        if (status.includes('成功') || status.includes('完成')) return 'success';
        if (status.includes('错误') || status.includes('失败')) return 'error';
        return 'processing';
    }

    function calculateNodePositions(constraints) {
        const positions = [];
        const graphWidth = 300;
        const graphHeight = 200;
        const nodeRadius = 30;
        
        constraints.forEach((constraint, index) => {
            let x, y;
            
            if (constraint.parent_id) {
                // 子节点位置
                const parentIndex = constraints.findIndex(c => c.id === constraint.parent_id);
                const parentPos = positions[parentIndex] || { x: graphWidth / 2, y: 50 };
                x = parentPos.x + (Math.random() - 0.5) * 100;
                y = parentPos.y + 80;
            } else {
                // 根节点位置
                x = 50 + (index * 80) % (graphWidth - 100);
                y = 50 + Math.floor(index / 3) * 60;
            }
            
            // 确保节点在边界内
            x = Math.max(nodeRadius, Math.min(graphWidth - nodeRadius, x));
            y = Math.max(nodeRadius, Math.min(graphHeight - nodeRadius, y));
            
            positions.push({ x, y });
        });
        
        return positions;
    }

    function createConnection(parent, child, container) {
        const parentElement = container.querySelector(`[data-id="${parent.id}"]`);
        const childElement = container.querySelector(`[data-id="${child.id}"]`);
        
        if (!parentElement || !childElement) return;
        
        const parentRect = parentElement.getBoundingClientRect();
        const childRect = childElement.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        
        const x1 = parentRect.left + parentRect.width / 2 - containerRect.left;
        const y1 = parentRect.top + parentRect.height / 2 - containerRect.top;
        const x2 = childRect.left + childRect.width / 2 - containerRect.left;
        const y2 = childRect.top + childRect.height / 2 - containerRect.top;
        
        const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
        const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
        
        const connection = document.createElement('div');
        connection.className = 'constraint-connection fork';
        connection.style.width = length + 'px';
        connection.style.left = x1 + 'px';
        connection.style.top = y1 + 'px';
        connection.style.transform = `rotate(${angle}deg)`;
        
        container.appendChild(connection);
    }

    function makeDecision(decision) {
        const actionRequired = document.getElementById('action-required');
        actionRequired.style.display = 'none';
        
        // 添加决策到日志
        const decisionLog = document.getElementById('decision-log');
        const timestamp = new Date().toLocaleTimeString('zh-CN', {hour12: false});
        const decisionText = decision === 'approve' ? '批准并继续项目' : '暂停项目检测';
        const logEntry = document.createElement('div');
        logEntry.style.color = decision === 'approve' ? '#4CAF50' : '#F44336';
        logEntry.textContent = `[${timestamp}] 项目经理决策: ${decisionText}`;
        decisionLog.appendChild(logEntry);
        decisionLog.scrollTop = decisionLog.scrollHeight;
        
        // 发送决策到服务器
        socket.emit('pm_decision', { decision: decision, timestamp: timestamp });
        
        console.log('项目经理决策:', decision);
    }

    function showDecisionRequired(question) {
        const actionRequired = document.getElementById('action-required');
        const decisionQuestion = document.getElementById('decision-question');
        
        decisionQuestion.textContent = question;
        actionRequired.style.display = 'block';
    }

    // --- 事件监听器 ---

    // 全局变量
    let currentDocPath = '';

    // 检查功能
    function startCheck() {
        if (!currentDocPath) {
            alert('请先设置目标设计文档目录路径');
            return;
        }
        
        console.log('开始检查目录:', currentDocPath);
        
        // 发送检查请求
        socket.emit('start_check', { doc_path: currentDocPath });
        
        // 更新按钮状态
        const startCheckBtn = document.getElementById('start-check-btn');
        startCheckBtn.disabled = true;
        startCheckBtn.textContent = '检查中...';
        
        // 显示进度
        const progressElement = document.getElementById('governance-progress');
        if (progressElement) {
            progressElement.style.display = 'block';
        }
    }

    // 治理功能
    function startGovernance() {
        if (!currentDocPath) {
            alert('请先设置目标设计文档目录路径');
            return;
        }
        
        console.log('开始治理目录:', currentDocPath);
        
        // 发送治理请求
        socket.emit('start_governance', { doc_path: currentDocPath });
        
        // 更新按钮状态
        const startGovernanceBtn = document.getElementById('start-governance-btn');
        startGovernanceBtn.disabled = true;
        startGovernanceBtn.textContent = '治理中...';
        
        // 显示进度
        const progressElement = document.getElementById('governance-progress');
        if (progressElement) {
            progressElement.style.display = 'block';
        }
    }

    // 生成代码功能
    function generateCode() {
        console.log('生成代码功能暂未实现');
        alert('生成代码功能将在后续版本中实现');
    }

    // 设置文档路径
    function setDocPath() {
        const path = prompt('请输入目标设计文档目录路径:');
        if (path && path.trim()) {
            currentDocPath = path.trim();
            console.log('设置文档路径:', currentDocPath);
            
            // 更新详细区显示
            const detailContent = document.getElementById('detail-content');
            if (detailContent) {
                detailContent.innerHTML = `
                    <div style="color: #4CAF50; font-weight: bold; margin-bottom: 0.5rem;">📁 文档路径已设置</div>
                    <div style="margin-bottom: 0.5rem;">
                        <strong>路径:</strong> ${currentDocPath}
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <strong>状态:</strong> 就绪
                    </div>
                    <div style="font-size: 0.7rem; color: #666;">
                        现在可以点击"检查"或"治理"按钮开始处理
                    </div>
                `;
            }
        }
    }

    // 项目相关功能
    function refreshProjects() {
        console.log('刷新项目列表');
        // 这里可以添加刷新项目的逻辑
        alert('项目列表刷新功能将在后续版本中实现');
    }

    function createNewProject() {
        console.log('创建新项目');
        // 这里可以添加创建新项目的逻辑
        alert('创建新项目功能将在后续版本中实现');
    }

    // --- Socket.IO事件处理 ---

    socket.on('connect', () => {
        console.log('Socket连接成功');
    });

    socket.on('disconnect', () => {
        console.log('Socket连接断开');
    });

    socket.on('check_progress', (data) => {
        console.log('检查进度更新:', data);
        
        // 更新进度显示
        const progressText = document.getElementById('progress-text');
        progressText.textContent = data.progress + '%';
        
        // 更新决策日志
        if (data.log) {
            const decisionLog = document.getElementById('decision-log');
            const logEntry = document.createElement('div');
            logEntry.textContent = data.log;
            decisionLog.appendChild(logEntry);
            decisionLog.scrollTop = decisionLog.scrollHeight;
        }
        
        // 检查是否需要决策
        if (data.requires_decision) {
            showDecisionRequired(data.decision_question);
        }
        
        // 更新其他状态
        if (data.health_report) {
            renderRiskAssessment(data.health_report);
        }
        
        if (data.knowledge_base) {
            renderKnowledgeBase(data.knowledge_base);
        }
        
        if (data.pm_status) {
            renderPMStatus(data.pm_status);
        }
        
        if (data.process_overview) {
            renderProcessOverview(data.process_overview);
        }
        
        if (data.project_output) {
            renderProjectOutput(data.project_output);
        }
    });

    socket.on('governance_progress', (data) => {
        console.log('治理进度更新:', data);
        
        // 更新进度显示
        const progressText = document.getElementById('progress-text');
        progressText.textContent = data.progress + '%';
        
        // 更新决策日志
        if (data.log) {
            const decisionLog = document.getElementById('decision-log');
            const logEntry = document.createElement('div');
            logEntry.textContent = data.log;
            decisionLog.appendChild(logEntry);
            decisionLog.scrollTop = decisionLog.scrollHeight;
        }
        
        // 检查是否需要决策
        if (data.requires_decision) {
            showDecisionRequired(data.decision_question);
        }
        
        // 更新其他状态
        if (data.health_report) {
            renderRiskAssessment(data.health_report);
        }
        
        if (data.knowledge_base) {
            renderKnowledgeBase(data.knowledge_base);
        }
        
        if (data.pm_status) {
            renderPMStatus(data.pm_status);
        }
        
        if (data.process_overview) {
            renderProcessOverview(data.process_overview);
        }
        
        if (data.project_output) {
            renderProjectOutput(data.project_output);
        }
    });

    socket.on('check_complete', (data) => {
        console.log('检查完成:', data);
        
        // 恢复按钮状态
        startCheckBtn.disabled = false;
        startCheckBtn.textContent = '检查';
        
        // 隐藏进度
        const progressElement = document.getElementById('governance-progress');
        progressElement.style.display = 'none';
        
        // 更新最终状态
        if (data.health_report) {
            renderRiskAssessment(data.health_report);
        }
        
        if (data.knowledge_base) {
            renderKnowledgeBase(data.knowledge_base);
        }
        
        alert('检查完成！请查看风险评估结果。');
    });

    socket.on('governance_complete', (data) => {
        console.log('治理完成:', data);
        
        // 恢复按钮状态
        startGovernanceBtn.disabled = false;
        startGovernanceBtn.textContent = '治理';
        
        // 隐藏进度
        const progressElement = document.getElementById('governance-progress');
        progressElement.style.display = 'none';
        
        // 更新最终状态
        if (data.project_output) {
            renderProjectOutput(data.project_output);
        }
        
        alert('治理完成！请查看项目交付结果。');
    });

    socket.on('error', (data) => {
        console.error('错误:', data);
        alert('发生错误: ' + data.message);
        
        // 恢复按钮状态
        startCheckBtn.disabled = false;
        startCheckBtn.textContent = '检查';
        startGovernanceBtn.disabled = false;
        startGovernanceBtn.textContent = '治理';
        
        // 隐藏进度
        const progressElement = document.getElementById('governance-progress');
        progressElement.style.display = 'none';
    });

    // --- 左侧菜单控制 ---
    
    let menuTimeout;
    const leftMenuTrigger = document.getElementById('leftMenuTrigger');
    const leftMenu = document.getElementById('leftMenu');

    if (leftMenuTrigger && leftMenu) {
        leftMenuTrigger.addEventListener('mouseenter', () => {
            clearTimeout(menuTimeout);
            leftMenu.classList.add('show');
        });

        leftMenu.addEventListener('mouseenter', () => {
            clearTimeout(menuTimeout);
        });

        leftMenu.addEventListener('mouseleave', () => {
            menuTimeout = setTimeout(() => {
                leftMenu.classList.remove('show');
            }, 1000);
        });

        leftMenuTrigger.addEventListener('mouseleave', () => {
            menuTimeout = setTimeout(() => {
                leftMenu.classList.remove('show');
            }, 1000);
        });
    }

    function openConfigCenter() {
        window.open('/config', '_blank');
    }

    // --- 初始化渲染 ---
    
    renderRiskAssessment(mockHealthReport);
    renderKnowledgeBase(mockKnowledgeBase);
    renderConstraintReview(null); // Initial empty state
    renderPMStatus(mockPMStatus);
    renderProcessOverview(mockProcessOverview);
    renderDecisionCenter(mockDecisionCenterLog);
    renderProjectOutput(mockProjectOutput);

    console.log("V2 Project Manager UI 初始化完成");
});

// --- 知识库交互函数 ---

function showConstraintDetail(constraintId) {
    console.log('显示约束详情:', constraintId);

    // V4.2 AtomicConstraint详情数据
    const constraintDetails = {
        'GB001': {
            id: 'GB001',
            category: 'boundary_condition',
            type: 'response_time',
            params: {
                target_entity: 'api_request',
                max_ms: 1000,
                unit: 'milliseconds',
                on_exceed_action: 'return_http_429'
            },
            description: '所有面向用户的API必须在1000ms内响应',
            source_block_id: 'block_001',
            parent_id: null
        },
        'LB002': {
            id: 'LB002',
            category: 'constraint',
            type: 'user_service',
            params: {
                target_entity: 'UserService',
                max_concurrent: 500,
                pool_size: 100,
                timeout_ms: 5000
            },
            description: '用户服务最大并发连接数限制(引用式分叉)',
            source_block_id: 'block_002',
            parent_id: 'GB001'
        },
        'BC003': {
            id: 'BC003',
            category: 'boundary_condition',
            type: 'response_time',
            params: {
                target_entity: 'core_api',
                max_ms: 200,
                unit: 'milliseconds',
                priority: 'high'
            },
            description: '核心API响应时间边界条件',
            source_block_id: 'block_003',
            parent_id: 'GB001'
        },
        'SM004': {
            id: 'SM004',
            category: 'state_machine',
            type: 'user_session',
            params: {
                target_entity: 'UserSession',
                states: ['idle', 'active', 'expired'],
                transitions: ['login', 'logout', 'timeout'],
                diagram_type: 'mermaid_state_diagram_v2'
            },
            description: '用户会话状态机验证规则',
            source_block_id: 'block_004',
            parent_id: null
        }
    };

    const detail = constraintDetails[constraintId];
    if (!detail) return;

    // 更新约束审查区域 - V4.2 AtomicConstraint完整结构展示
    const reviewContent = document.getElementById('constraint-review-content');

    // 生成参数树HTML
    const paramsHtml = Object.entries(detail.params).map(([key, value]) =>
        `<div class="param-node">
            <span class="param-key">${key}</span>
            <span class="param-value">${typeof value === 'object' ? JSON.stringify(value) : value}</span>
        </div>`
    ).join('');

    reviewContent.innerHTML = `
        <div class="constraint-detail-enhanced">
            <div class="constraint-header">
                <span class="constraint-id">${detail.id}</span>
                <span class="constraint-category category-${detail.category.replace('_', '-')}">${detail.category}</span>
            </div>

            <div class="constraint-section">
                <div class="section-title">🔧 核心字段</div>
                <div class="field-grid">
                    <div class="field-item">
                        <span class="field-label">category:</span>
                        <span class="field-value">${detail.category}</span>
                    </div>
                    <div class="field-item">
                        <span class="field-label">type:</span>
                        <span class="field-value">${detail.type}</span>
                    </div>
                </div>
            </div>

            <div class="constraint-section">
                <div class="section-title">📦 参数结构 (params)</div>
                <div class="params-tree">
                    ${paramsHtml}
                </div>
            </div>

            <div class="constraint-section">
                <div class="section-title">🔗 血统关系</div>
                <div class="lineage-info">
                    <div class="lineage-item">
                        <span class="lineage-label">parent_id:</span>
                        <span class="lineage-value">${detail.parent_id || 'null (根约束)'}</span>
                    </div>
                    <div class="lineage-item">
                        <span class="lineage-label">source_block_id:</span>
                        <span class="lineage-value">${detail.source_block_id}</span>
                    </div>
                </div>
            </div>

            <div class="constraint-section">
                <div class="section-title">📝 描述</div>
                <div style="background: rgba(255, 255, 255, 0.02); padding: 0.5rem; border-radius: 4px; font-size: 0.85rem; line-height: 1.4;">
                    ${detail.description}
                </div>
            </div>
        </div>
    `;

    // 高亮选中的节点
    document.querySelectorAll('.constraint-node').forEach(node => {
        node.style.transform = '';
        node.style.zIndex = '';
    });

    const selectedNode = document.querySelector(`[onclick="showConstraintDetail('${constraintId}')"]`);
    if (selectedNode) {
        selectedNode.style.transform = 'scale(1.3)';
        selectedNode.style.zIndex = '15';
    }
}

// --- V2专用函数（通用函数已移至nine_grid_base.js） ---

// --- 知识库帮助弹窗控制函数 ---

/**
 * 显示知识库帮助弹窗
 * @param {Event} event - 点击事件
 */
function showKnowledgeBaseHelp(event) {
    event.stopPropagation();

    const popup = document.getElementById('knowledge-help-popup');
    const helpIcon = event.currentTarget;

    if (!popup || !helpIcon) return;

    // 计算弹窗位置
    const iconRect = helpIcon.getBoundingClientRect();
    const knowledgeArea = helpIcon.closest('.grid-area');
    const areaRect = knowledgeArea.getBoundingClientRect();

    // 设置弹窗位置（相对于知识库区域）
    popup.style.position = 'absolute';
    popup.style.top = '2rem';
    popup.style.right = '1rem';
    popup.style.left = 'auto';
    popup.style.bottom = 'auto';

    // 显示弹窗
    popup.classList.add('show');

    // 添加全局点击监听器来关闭弹窗
    setTimeout(() => {
        document.addEventListener('click', handleOutsideClick);
    }, 100);

    console.log('显示知识库帮助弹窗');
}

/**
 * 隐藏知识库帮助弹窗
 */
function hideKnowledgeBaseHelp() {
    const popup = document.getElementById('knowledge-help-popup');
    if (!popup) return;

    popup.classList.remove('show');

    // 移除全局点击监听器
    document.removeEventListener('click', handleOutsideClick);

    console.log('隐藏知识库帮助弹窗');
}

/**
 * 处理弹窗外部点击
 * @param {Event} event - 点击事件
 */
function handleOutsideClick(event) {
    const popup = document.getElementById('knowledge-help-popup');
    const helpIcon = document.querySelector('.help-icon');

    if (!popup || !helpIcon) return;

    // 如果点击的不是弹窗内部或帮助图标，则关闭弹窗
    if (!popup.contains(event.target) && !helpIcon.contains(event.target)) {
        hideKnowledgeBaseHelp();
    }
}
